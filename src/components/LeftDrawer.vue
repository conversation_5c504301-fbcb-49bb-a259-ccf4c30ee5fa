<template>
  <div>
    <q-drawer
      class="bg-grey-2 mmm-drawer"
      show-if-above
      :width="340"
      v-model="open"
      :breakpoint="800"
    >
      <div class="q-px-lg">
        <div style="height: 72px" class="row items-center">
          <img src="images/mmm-logo.png" style="height: 48px" />
        </div>
        <q-btn
          label="Create"
          icon="add"
          class="q-mb-xs q-py-sm bg-white mmm-create"
          no-caps
          @click="onNew(true)"
          v-if="role === 'Admin'"
        >
        </q-btn>
        <date-picker />

        <q-select
          dense
          filled
          :model-value="provider"
          use-input
          hide-selected
          fill-input
          input-debounce="0"
          :options="providerOptions"
          @filter="filterFn"
          @input-value="setProvider"
          placeholder="Filter by provider"
          unelevated
          no-caps
          v-if="role === 'Admin'"
        >
          <template v-slot:no-option>
            <q-item>
              <q-item-section class="text-grey"> No results </q-item-section>
            </q-item>
          </template>
          <template v-slot:append>
            <q-icon
              v-if="provider"
              name="close"
              size="1em"
              @click.stop.prevent="provider = ''"
              class="cursor-pointer"
            />
          </template>
        </q-select>
        <q-select
          dense
          filled
          v-model="eventType"
          :options="eventOptions"
          style="min-width: 150px"
          class="q-mt-lg"
          placeholder="Filter by event"
          use-input
          hide-selected
          fill-input
          @input-value="onEventFilter"
          unelevated
          no-caps
        >
          <template v-slot:append>
            <q-icon
              v-if="eventType"
              name="close"
              size="1em"
              @click.stop.prevent="eventType = ''"
              class="cursor-pointer"
            />
          </template>
        </q-select>
        <template v-if="isAppointment">
          <q-select
            dense
            filled
            v-model="appointmentType"
            :options="appointmentOptions"
            style="min-width: 150px"
            class="q-mt-lg"
            placeholder="Filter by appointment"
            use-input
            fill-input
            hide-selected
            @input-value="onAppointmentFilter"
            unelevated
            no-caps
          >
            <template v-slot:append>
              <q-icon
                v-if="appointmentType"
                name="close"
                size="1em"
                @click.stop.prevent="appointmentType = ''"
                class="cursor-pointer"
              />
            </template>
          </q-select>
          <q-input
            dense
            filled
            v-model="patientName"
            placeholder="Search by patient"
            class="q-mt-lg"
            unelevated
            no-caps
          >
            <template v-slot:prepend>
              <q-icon name="search" />
            </template>
          </q-input>
          <q-btn
            class="q-mt-xl full-width draw-close-btn"
            color="primary"
            dense
            label="close"
            @click="onClose"
            outline
            unelevated
            rounded
          />
        </template>
      </div>
    </q-drawer>
    <q-dialog v-model="appointment" persistent>
      <template #default>
        <appointment-modal v-bind="providerField" @close="appointment = false" />
      </template>
    </q-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { storeToRefs } from 'pinia'
import { useStaffStore } from 'stores/staff'
import { useFilterStore } from 'stores/filter'
import { useAuthStore } from 'stores/user'
import DatePicker from 'components/timer/DatePicker.vue'
import type { AppointmentType, EventType, ProviderField } from 'src/components/models'
import AppointmentModal from 'components/event/AppointmentModal.vue'
import { DEFAULT_PROVIDER_FIELD } from 'components/variable.js'
import { generateTimeList } from 'src/utils'
import moment from 'moment'
const filterStore = useFilterStore()
const staffStore = useStaffStore()
const authStore = useAuthStore()
const { role } = storeToRefs(authStore)
const { _staffs } = storeToRefs(staffStore)
const { _patientName, _open, _date } = storeToRefs(filterStore)
const provider = ref()
const eventType = ref()
const appointmentType = ref()
const appointment = ref(false)
const timeoff = ref(false)
const providerField = ref<ProviderField>(DEFAULT_PROVIDER_FIELD)

const open = computed({
  get: () => _open.value,
  set: () => filterStore.setOpen(),
})
const patientName = computed({
  get: () => _patientName.value,
  set: (val) => filterStore.changePatient(val),
})
const allOptions = computed(() => {
  return _staffs.value.map((el) => el.name)
})
const providerOptions = ref<string[]>([])
const eventOptions = ref<EventType[]>(['Appointment', 'Time Off'])
const appointmentOptions = ref<AppointmentType[]>(['Acupuncture', 'Massage', 'Medical'])
const isAppointment = computed(() => !eventType.value || eventType.value === 'Appointment')

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const filterFn = (val: any, update: any) => {
  update(() => {
    const needle = val.toLocaleLowerCase()
    providerOptions.value = allOptions.value.filter(
      (v) => !!v && v.toLocaleLowerCase().indexOf(needle) > -1,
    )
  })
}
const setProvider = (val: string) => {
  const staff = _staffs.value.find((el) => el.name === val)
  if (staff) {
    filterStore.updateProvider(staff.id)
  } else {
    filterStore.updateProvider('')
  }
  provider.value = val
}
const onEventFilter = (val: string) => {
  filterStore.updateEvent(val as EventType)
}
const onAppointmentFilter = (val: string) => {
  filterStore.updateAppointment(val as AppointmentType)
}
const onClose = () => {
  filterStore.setOpen()
}
const onNew = (isAppointment: boolean) => {
  const { time } = generateTimeList()
  providerField.value = {
    ...DEFAULT_PROVIDER_FIELD,
    start: `${moment(_date.value, 'dddd, MMMM D, YYYY').format('YYYY-MM-DD')} ${moment(time, 'hh:mm A').format('HH:mm')}`,
  }

  if (isAppointment) appointment.value = true
  else timeoff.value = true
}
</script>
<style lang="scss">
.mmm-drawer {
  .draw-close-btn {
    display: none;
    @media only screen and (max-width: 800px) {
      display: block;
    }
  }
  .mmm-create {
    border-radius: 12px;
    padding-bottom: 10px;
    padding-top: 10px;
    padding-left: 24px;
    padding-right: 24px;
  }
  .q-select,
  .q-input {
    border-radius: 10px;
    min-width: 100px;
    background-color: #e9eef6;
    .q-field__control {
      border-radius: 10px;
      //    background: none;
      &::after {
        background: transparent;
      }
    }
  }
}
</style>
