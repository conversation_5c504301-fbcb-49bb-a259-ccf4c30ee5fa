<template>
  <div class="bg-white" style="border-top-left-radius: 20px">
    <div class="mmm-calendar-header row q-pl-lg items-center text-grey-9">
      <q-icon name="event_available" size="1.7em" />
      <div class="text-h6 q-ml-xs">{{ realEvents.length }}</div>
      <div class="text-body2 q-ml-xs">Total Appointments</div>
    </div>
    <vue-cal
      :disable-views="['years', 'year', 'month']"
      active-view="day"
      :split-days="providers"
      :events="events"
      sticky-split-labels
      :time-from="timeRange.min"
      :time-step="30"
      :time-to="timeRange.max"
      :selected-date="selectedDate"
      time-format="hh:mm AM"
      :watch-real-time="true"
      :time-cell-height="timeRange.height"
      :min-cell-width="minWidth"
      :min-split-width="minWidth"
    >
      <template #split-label="{ split }">
        <div class="full-height relative-position full-width">
          <div class="absolute-center text-grey-9 full-width">
            <div style="width: fit-content; margin: auto">
              <div class="gt-md text-body1">
                <strong :style="`color: ${split.color}`">{{ split.label }}</strong>
              </div>
              <div class="lt-lg text-body2">
                <strong :style="`color: ${split.color}`">{{ split.label }}</strong>
              </div>
              <div class="text-caption gt-md mmm-split-description">
                Appointments: {{ splitLen(split.id) }}
              </div>
              <div class="text-caption lt-lg mmm-split-description text-center">
                ({{ splitLen(split.id) }})
              </div>
            </div>
          </div>
        </div>
      </template>
      <template #event="{ event }">
        <event-component v-bind="event" />
      </template>
      <template #cell-content="{}">
        <div @click="onCellClicked" class="highlight-cell"></div>
        <div class="highlight-timeframe"></div>
      </template>
      <template #time-cell="{ hours, minutes }">
        <div :class="{ 'vuecal__time-cell-line': true, hours: !minutes }" style="font-size: 0.6rem">
          {{ moment(`${hours}:${minutes}`, 'H:m').format('hh:mm A') }}
        </div>
      </template>
    </vue-cal>
    <q-dialog v-model="appointment" persistent>
      <template #default>
        <appointment-modal v-bind="providerField" @close="appointment = false" />
      </template>
    </q-dialog>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, watch, ref } from 'vue'
import moment from 'moment'
import VueCal from 'vue-cal'
import EventComponent from 'components/event/EventComponent.vue'
import 'vue-cal/dist/vuecal.css'
import { useEvent } from 'src/composables/useEvent'
import { DEFAULT_PROVIDER_FIELD } from 'components/variable.js'
import AppointmentModal from 'components/event/AppointmentModal.vue'
import type { AppointmentType, ProviderField } from '../models'
const minWidth = ref(240)
const appointment = ref(false)
const { selectedDate, timeRange, events, providers } = useEvent()
const realEvents = computed(() =>
  events.value.filter(
    (el) => !el.non_event && el.event === 'Appointment' && el.status !== 'Cancelled' && el.status !== 'No Show',
  ),
)
const providerField = ref<ProviderField>(DEFAULT_PROVIDER_FIELD)
const splitLen = computed(
  () => (id: number) => realEvents.value.filter((el) => el.split === id).length,
)
onMounted(() => {
  setHighlight()
})
const isEmptyInArea = (elements: NodeListOf<Element>, hoverTop: number) => {
  for (let i = 0; i < elements.length; i++) {
    const element = elements[i] as HTMLElement
    try {
      const hoverHeight = timeRange.value.height || 0
      const elementTop = parseInt(element.style.top)
      const elementHeight = parseInt(element.style.height)
      const bottom = Math.min(elementTop + elementHeight, hoverTop + hoverHeight)
      const top = Math.max(elementTop, hoverTop)
      if (bottom > top) return false
    } catch {
      /** */
    }
  }
  return true
}
const getAppointment = (pid: string) => {
  const provider = providers.value.find((el) => el.pid === pid)
  if (!provider) return ''
  if (provider.role === 'NP' || provider.role == 'PA') return 'Medical'
  else if (provider.role === 'Massage Therapist') return 'Massage'
  else if (provider.role === 'Acupuncturist') return 'Acupuncture'
  return ''
}
const onCellClicked = () => {
  const highlightTimeFrame = document.querySelector('.highlight-timeframe.active') as HTMLElement
  try {
    const id = parseInt(highlightTimeFrame.ariaValueText || '0')
    const time = highlightTimeFrame.innerText
    const provider = providers.value.find((el) => el.id === id)
    if (time && id > 0 && provider) {
      providerField.value.start = `${moment(selectedDate.value).format('YYYY-MM-DD')} ${moment(time, 'h:mm A').format('HH:mm')}`
      providerField.value.duration = '30 minutes'
      providerField.value.pid = provider.pid
      providerField.value.event = 'Appointment'
      providerField.value.appointment = getAppointment(provider.pid) as AppointmentType
      appointment.value = true
    }
  } catch {
    /** */
  }
}
const setHighlight = () => {
  const eventCols = document.querySelectorAll('.vuecal__cell-content')
  eventCols.forEach((col, index) => {
    const eventBox = col as HTMLElement
    eventBox.addEventListener('mousemove', (event) => {
      const highlightCell = eventBox.querySelector('.highlight-cell') as HTMLElement
      const highlightTimeFrame = eventBox.querySelector('.highlight-timeframe') as HTMLElement
      if (highlightCell) {
        const rect = eventBox.getBoundingClientRect()
        const y = event.clientY - rect.top
        if (timeRange.value.height) {
          const eventElements = eventBox.querySelectorAll('.vuecal__event')
          const length = Math.floor(y / timeRange.value.height)
          const top = length * timeRange.value.height
          if (isEmptyInArea(eventElements, top)) {
            highlightCell.style.top = `${top}px`
            highlightCell.style.height = `${timeRange.value.height}px`
            highlightCell.style.display = 'block'
            if (highlightTimeFrame) {
              highlightTimeFrame.style.display = 'block'
              highlightTimeFrame.style.lineHeight = `${timeRange.value.height}px`
              highlightTimeFrame.style.top = `${top}px`
              highlightTimeFrame.style.right = '6px'
              highlightTimeFrame.classList.add('active')
              highlightTimeFrame.ariaValueText = `${index + 1}`
              const minutes = timeRange.value.min + 30 * length
              const mTime = moment()
              mTime.hour(0)
              mTime.minute(0)
              mTime.second(0)
              highlightTimeFrame.innerText = mTime.add(minutes, 'minutes').format('h:mm A')
            }
          } else {
            highlightCell.style.display = 'none'
            highlightTimeFrame.style.display = 'none'
          }
        }
      }
    })
    eventBox.addEventListener('mouseleave', () => {
      const highlightCells = document.querySelectorAll('.highlight-cell')
      const highlightTimeFrames = document.querySelectorAll('.highlight-timeframe')
      highlightCells.forEach((cell) => {
        const box = cell as HTMLElement
        box.style.display = 'none'
        box.style.height = '0px'
      })
      highlightTimeFrames.forEach((cell) => {
        const box = cell as HTMLElement
        box.style.display = 'none'
        cell.classList.remove('active')
        box.ariaValueText = ''
        box.innerText = ''
      })
    })
  })
}
watch(selectedDate, () => {
  setTimeout(() => {
    setHighlight()
  }, 1000)
})
</script>
<style lang="scss">
.mmm-calendar-header {
  height: 52px;
  border-top-left-radius: 20px;
  border-bottom: 1px solid #eeeeee;
}
.vuecal {
  box-shadow: none;
  .vuecal__header {
    display: none;
    /*
    height: 48px;
    @media only screen and (max-width: 800px) {
      width: 100%;
    }
    .vuecal__split-days-headers {
      margin-left: 4em;
      height: 100%;
      .day-split-header {
        border-left: 1px solid #dddddd;
        .mmm-split-description {
          margin-top: -4px;
        }
      }
    }
    .header-title {
      font-size: 0.875rem;
      @media only screen and (max-width: 800px) {
        font-size: 0.5rem;
      }
    }
    .vuecal__menu {
      display: none;
    }
    .vuecal__title-bar {
      display: none;
    }
    */
  }
  .vuecal__body {
    .vuecal__time-column {
      margin-top: 48px !important;
    }
    .vuecal__split-days-headers {
      height: 48px;
      .day-split-header {
        border-left: 1px solid #dddddd;
        .mmm-split-description {
          margin-top: -4px;
        }
      }
    }
    .vuecal__bg {
      overflow: hidden;
    }
    .vuecal__time-column {
      width: 4em;
    }
    .vuecal__cells.day-view {
      position: relative;
      .vuecal__cell {
        &::before {
          right: 0;
        }
        .vuecal__cell-content {
          margin-right: 1px;
          border-left: 1px solid rgba(0, 0, 0, 0.1);
          position: relative;
          .vuecal__event {
            padding-left: 1px;
            &:not(.vuecal__event--background) {
              background-color: transparent;
              z-index: 1;
              &:hover {
                z-index: 2;
              }
            }
          }
          .vuecal__event--focus {
            box-shadow: none;
          }
        }
      }
    }
  }
}
.vuecal__no-event {
  display: none;
}
.vuecal__time-cell {
  cursor: pointer;
}
.highlight-timeframe {
  position: absolute;
  z-index: 1; /* Ensures it's behind the content */
  color: black;
}
.highlight-cell {
  position: absolute;
  background-color: #eaf6ff;
  z-index: 2; /* Ensures it's behind the content */
  opacity: 0.6;
  border: 2px dashed #6abdff;
  display: none;
  width: 100%;
  cursor: pointer;
}
.np-pa-flex-width {
  flex: 2 !important;
}
.vuecal__cell-split {
  flex-grow: 1;
  flex-basis: 0 !important;
}
.vuecal__event--background {
  z-index: -1;
}
</style>
