<template>
  <vue-cal
    :disable-views="['years', 'year', 'month']"
    active-view="week"
    :events="events"
    :time-from="timeFrom"
    :time-to="timeTo"
    :time-step="15"
    :selected-date="selectedDate"
    time-format="hh:mm AM"
    :timeCellHeight="60"
    :min-cell-width="200"
  >
    <template #split-label="{ split }">
      <strong class="text-body1" :style="`color: ${split.color}`">{{ split.label }}</strong>
    </template>
    <template #event="{ event }">
      <event-component v-bind="event" />
    </template>
  </vue-cal>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { storeToRefs } from 'pinia'
import { useCenterStore } from 'stores/center'
import { useFilterStore } from 'stores/filter.js'
import moment from 'moment'
import VueCal from 'vue-cal'
import EventComponent from 'components/event/EventComponent.vue'
import 'vue-cal/dist/vuecal.css'
import type { ProviderField } from 'src/components/models'
import { US_TIME_FORMAT } from 'src/components/variable'
const centerStore = useCenterStore()
const filterStore = useFilterStore()
const { providers } = storeToRefs(centerStore)
const { _date } = storeToRefs(filterStore)
const selectedDate = computed(() => {
  return moment(_date.value, US_TIME_FORMAT).toDate()
})
const events = computed(() => {
  const events: ProviderField[] = []
  providers.value.forEach((provider, index) => {
    provider.fields.forEach((field) => {
      if (isActiveDate(field.start)) {
        events.push({
          ...field,
          split: index + 1,
        })
      }
    })
  })
  return events
})

const timeFrom = computed(() => {
  if (!events.value || events.value.length === 0) return 0
  const hours = events.value
    .map((el) => moment(el.start, 'YYYY-MM-DD HH:mm').hour())
    .filter((el) => !isNaN(el))
  const minHour = Math.min(...hours)
  return minHour * 60
})
const timeTo = computed(() => {
  if (!events.value || events.value.length === 0) return 60 * 3
  const hours = events.value
    .map((el) => moment(el.end, 'YYYY-MM-DD HH:mm').hour())
    .filter((el) => !isNaN(el))
  const maxHour = Math.max(...hours)
  return (maxHour + 1) * 60
})
const isActiveDate = (date: string | Date) => {
  const momentA = moment(selectedDate.value)
  const momentB = moment(date, 'YYYY-MM-DD')
  return momentA.isSame(momentB)
}
</script>
<style lang="scss">
.vuecal {
  .vuecal__body {
    .vuecal__cell--today {
      background-color: transparent;
    }
    .vuecal__cell--selected {
      background-color: transparent;
    }
    .vuecal__weekdays-headings {
      padding-top: 0.7em;
    }
    .vuecal__cells {
      border-bottom: 1px solid rgba(196, 196, 196, 0.25);
    }
  }
}
.vuecal__no-event {
  display: none;
}
</style>
