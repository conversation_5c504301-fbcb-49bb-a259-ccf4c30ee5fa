import type { AppointmentType, EventType, ProviderField, StatusType } from './models'
export const MAIN_BASE_ID = 'appbAXrcL96xQIa1C'
export const SCHEDULE_TABLE_ID = 'tblhJxOiGVx9pWvRL'
export const SCHEDULE_VIEW_ID = 'viwYoTefj1zo3bzdc'
export const STAFF_TABLE_ID = 'tbl788T03WqML4HB4'
export const STAFF_VIEW_ID = 'viwqyNv99U4qo7Spu'
export const CLIENT_TABLE_ID = 'tbl4I7rEbkPD5QBLD'
export const CLIENT_VIEW_ID = 'viwvRF0eEmLqNSi3A'
export const US_TIME_FORMAT = 'dddd, MMMM D, YYYY'
//export const US_TIME_FORMAT = 'MM/DD/YYYY'
export const MMM_CRED = 'mmm_credentials'
export const DEFAULT_PROVIDER_FIELD: ProviderField = {
  name: '',
  event: '' as EventType,
  start: '',
  end: '',
  duration: '',
  notes: '',
  appointment: '' as AppointmentType,
  patientId: [],
  status: '' as StatusType,
  split: 1,
  patientName: [],
  id: '',
}
export const WeekArray = [
  'Sunday',
  'Monday',
  'Tuesday',
  'Wednesday',
  'Thursday',
  'Friday',
  'Saturday',
]
export const GROUP_COLORS = [
  'blue',
  'purple',
  'red',
  'brown',
  'orange',
  'pink',
  'blue-grey',
  'indigo',
]
export const ADMIN_UIDS = [
  'm0CcRIYFFefieTA1qlY8rRXqQFx1',
  'VHEfqEhm2PMlszbxdwFsk3qFdTh1',
  'ER0mbXh7s6UvKYd4D1uUvVFNdVx2',
  'ExXPw4yd3NgTBYv7Ht2AgFcLuYI2',
  'OtYJtNVJPCSbnd9h2qb91ZxfENH3',
]
export const ACUS = ['glrNKRfOGoMwUrvsao1yVb5rUDd2', 'XUeQNkNG3UNZjuvTKdvDkv06kGA3']
