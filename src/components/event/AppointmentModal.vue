<template>
  <q-card v-if="event" style="min-width: 350px">
    <q-form @submit="onBeforeSave">
      <q-card-section class="row items-center">
        <div class="text-h6">{{ modalTitle }}</div>
        <q-space />
        <q-checkbox v-if="!isNew && role === 'Admin'" v-model="isCover" label="Cover" />
      </q-card-section>
      <q-linear-progress v-if="_clients.length === 0 && isNew" class="q-mb-md" indeterminate />
      <q-card-section class="q-pt-none">
        <div v-if="isNew" class="row items-center q-pb-lg">
          <q-btn
            class="col-grow q-mr-sm mmm-btn-appointement"
            :class="event.event === 'Appointment' ? 'active' : ''"
            label="Appointment"
            icon="event"
            no-caps
            :disable="readOnly || role !== 'Admin'"
            outline
            @click="event.event = 'Appointment'"
          />
          <q-btn
            btn
            class="col-grow q-ml-sm mmm-btn-appointement"
            :class="event.event === 'Time Off' ? 'active' : ''"
            label="Time Off"
            icon="schedule"
            :disable="readOnly || role !== 'Admin'"
            no-caps
            outline
            @click="event.event = 'Time Off'"
          />
        </div>
        <template v-if="!isTimeoff">
          <div class="row q-gutter-sm items-end">
            <div class="col">
              <q-select
                outlined
                dense
                v-model="patient"
                label="Patient"
                use-input
                hide-selected
                fill-input
                input-debounce="0"
                :readonly="role !== 'Admin'"
                :options="options"
                @filter="filterFn"
                :hide-bottom-space="false"
                :rules="[(val) => (val && val.length > 0) || 'Please type something']"
              >
                <template v-slot:no-option>
                  <q-item>
                    <q-item-section class="text-grey"> No results </q-item-section>
                  </q-item>
                </template>
              </q-select>
            </div>
            <div class="col-auto" v-if="selectedPatientId && !isNew">
              <q-btn
                outline
                dense
                color="primary"
                icon="open_in_new"
                label="View Profile"
                no-caps
                @click="openPatientProfile"
                class="q-mb-xs"
              />
            </div>
          </div>

          <q-select
            outlined
            dense
            v-model="event.appointment"
            :options="['Medical', 'Massage', 'Acupuncture']"
            label="Type"
            :rules="[(val) => (val && val.length > 0) || 'Please type something']"
            :hide-bottom-space="false"
            :readonly="readOnly"
          />
        </template>
        <date-input :cover="isCover" :date="date" :current-time="startTime" @change="onChageDate" />
        <q-select
          outlined
          dense
          v-model="provider"
          :options="_providers.map((el) => el.name)"
          label="Provider"
          :rules="[(val) => (val && val.length > 0) || 'Please type something']"
          :hide-bottom-space="false"
          :readonly="role !== 'Admin'"
        />
        <template v-if="!isCover">
          <q-select
            v-if="!isTimeoff"
            outlined
            dense
            v-model="event.status"
            :options="['Pending', 'Confirmed', 'Complete', 'Cancelled', 'No Show']"
            label="Status"
            :rules="[(val) => (val && val.length > 0) || 'Please type something']"
            :hide-bottom-space="false"
            :readonly="readOnly"
          />
          <div class="gt-md">
            <time-input label="Start Time" :value="startTime" @change="onChangeTime" />
          </div>
          <div class="lt-lg">
            <ios-timer-picker label="Start Time" :value="startTime" @change="onChangeTime" />
          </div>
          <q-select
            outlined
            dense
            v-model="event.duration"
            :readonly="readOnly"
            :options="['15 minutes', '30 minutes', '60 minutes']"
            label="Duration"
            :rules="[(val) => (val && val.length > 0) || 'Please type something']"
            :hide-bottom-space="false"
          />
          <q-input
            v-if="!isTimeoff"
            outlined
            dense
            type="textarea"
            v-model="event.notes"
            :readonly="readOnly"
          />
        </template>
      </q-card-section>
      <q-card-actions class="text-primary">
        <q-btn
          v-if="!isNew && !readOnly"
          flat
          label="Delete appointment"
          color="red"
          rounded
          unelevated
          no-caps
          @click="onOpenDeleteModal"
        />
        <q-space />
        <q-btn flat label="Cancel" v-close-popup rounded unelevated no-caps />
        <q-btn
          flat
          v-if="!readOnly || isCover"
          type="submit"
          label="Save"
          :disable="disabled"
          :loading="disabled"
          rounded
          unelevated
          no-caps
        />
      </q-card-actions>
    </q-form>
    <q-dialog v-model="alert">
      <q-card style="max-width: 380px">
        <q-card-section>
          <div class="text-h6 text-red">Alert</div>
        </q-card-section>

        <q-card-section class="q-pt-none text-body1">
          Time of the booking is not during the staff member’s working hours.
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="Cancel" color="primary" v-close-popup />
          <q-btn flat label="Agree" color="primary" @click="onSave" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-card>
</template>

<script setup lang="ts">
import moment from 'moment'
import type { ProviderField, StatusType } from '../models'
import { computed, ref, onMounted } from 'vue'
import { useScheduleStore } from 'stores/schedule.js'
import { useClientStore } from 'stores/client.js'
import { useCenterStore } from 'stores/center'
import { useAuthStore } from 'stores/user'
import { storeToRefs } from 'pinia'
import { US_TIME_FORMAT, MAIN_BASE_ID, CLIENT_TABLE_ID } from '../variable'
import TimeInput from 'components/timer/TimeInput.vue'
import DateInput from 'components/timer/DateInput.vue'
import IosTimerPicker from 'components/timer/IosTimerPicker.vue'
import { compareDate, generateTimeList } from 'src/utils'

const props = withDefaults(defineProps<ProviderField>(), {
  name: '',
  event: 'Appointment',
  start: '',
  end: '',
  duration: '',
  notes: '',
  appointment: 'Medical',
  status: '' as StatusType,
  split: 1,
  pid: '',
  group: '',
  cancelled: () => [],
})

const emit = defineEmits(['close'])
const scheduleStore = useScheduleStore()
const clientStore = useClientStore()
const centerStore = useCenterStore()
const authStore = useAuthStore()
const { role } = storeToRefs(authStore)
const { _clients } = storeToRefs(clientStore)
const { providers } = storeToRefs(centerStore)
const event = ref<ProviderField>()
const startTime = ref('')
const endTime = ref('')
const patient = ref('')
const date = ref('')
const provider = ref('')
const disabled = ref(false)
const alert = ref(false)
const isCover = ref(false)
const isNew = computed(() => !event.value?.id)
const modalTitle = computed(() => (isNew.value ? 'Create Appointment' : 'Edit Appointment'))
const readOnly = computed(() => isCover.value || role.value !== 'Admin')
const _providers = computed(() => {
  const selectedDate = moment(date.value, US_TIME_FORMAT).toDate()
  return providers.value
    .filter((el) => {
      if (el.startDate && compareDate(el.startDate, selectedDate, 'before')) {
        return false
      }
      if (el.inactiveDate && !compareDate(el.inactiveDate, selectedDate, 'before')) {
        return false
      }
      if (isCover.value) return true
      const flag = el.workingDays.some((el) => el.day === moment(selectedDate).weekday())
      if (!flag) {
        return el.fields.find((field) => compareDate(field.start, selectedDate, 'same'))
      }
      return true
    })
    .filter((provider) => {
      if (event.value?.event === 'Time Off') return true
      const appointment = event.value?.appointment
      if (!appointment) return true
      if (appointment === 'Medical') return provider.role === 'NP' || provider.role == 'PA'
      else if (appointment === 'Massage') return provider.role === 'Massage Therapist'
      else if (appointment === 'Acupuncture') return provider.role === 'Acupuncturist'
      return false
    })
})
const stringOptions = computed(() => _clients.value.map((el) => el.fields['Patient Name']))
const options = ref(stringOptions.value)
const isTimeoff = computed(() => event.value?.event === 'Time Off')
const selectedPatientId = computed(() => {
  if (!patient.value) return null
  const selectedClient = _clients.value.find((el) => el.fields['Patient Name'] === patient.value)
  return selectedClient?.id || null
})
onMounted(() => {
  event.value = { ...props }
  if (!props.event) event.value.event = 'Appointment'
  const _provider = providers.value.find((el) => el.id === event.value?.pid)
  if (_provider) provider.value = _provider.name
  patient.value = event.value.patientName[0] || ''
  if (!event.value.id) {
    if (props.start) onInitDateTime(props.start)
  } else {
    onInitDateTime(props.start)
  }
})
const onInitDateTime = (start: string | Date) => {
  startTime.value = moment(start, 'YYYY-MM-DD HH:mm').format('HH:mm')
  endTime.value = startTime.value
  date.value = moment(props.start).format(US_TIME_FORMAT)
  if (!startTime.value) {
    const { time } = generateTimeList()
    startTime.value = moment(time, 'hh:mm A').format('HH:mm')
  }
}
const checkWorkingHours = () => {
  const _provider = providers.value.find((el) => el.name === provider.value)
  if (_provider) {
    const mTime = moment.utc(event.value?.start).subtract(4, 'hours').format('H:mm')
    const day = moment(date.value, US_TIME_FORMAT).weekday()
    const workingDay = _provider.workingDays.find((el) => el.day === day)
    if (workingDay) {
      const time = moment(mTime, 'H:mm')
      const startTime = moment(`${workingDay.from}:00`, 'H:mm')
      const endTime = moment(`${workingDay.to}:00`, 'H:mm')
      return time.isBetween(startTime, endTime, undefined, '[)')
    }
  }
  return false
}
const onSave = async () => {
  if (event.value) {
    const _provider = providers.value.find((el) => el.name === provider.value)
    const providerId = _provider?.id || ''
    disabled.value = true
    if (isCover.value) {
      await scheduleStore.coverSchedule(providerId, event.value.id)
    } else {
      if (event.value.id) {
        await scheduleStore.updateSchedule(providerId, event.value)
        disabled.value = false
      } else {
        const _client = _clients.value.find((el) => el.fields['Patient Name'] === patient.value)
        const patientName = patient.value
        const patientId = _client?.id || ''
        event.value.patientName = [patientName]
        event.value.patientId = [patientId]
        await scheduleStore.createNewAppointement(providerId, event.value)
      }
    }
  }
  emit('close', false)
}
const onBeforeSave = async () => {
  if (event.value) {
    changeTime()
    const checked = checkWorkingHours()
    if (checked) {
      onSave()
    } else {
      alert.value = true
    }
  }
}

const changeTime = () => {
  try {
    const sTime = startTime.value || ''
    const eTime = endTime.value || ''
    const sHour = parseInt(sTime.split(':')[0] || '')
    const sMinute = parseInt(sTime.split(':')[1] || '')
    const eHour = parseInt(eTime.split(':')[0] || '')
    const eMinute = parseInt(eTime.split(':')[1] || '')
    const utcStart = `${moment(date.value, US_TIME_FORMAT).format('YYYY-MM-DDTHH:mm:ss.SSS')}Z`
    const utcEnd = `${moment(date.value, US_TIME_FORMAT).format('YYYY-MM-DDTHH:mm:ss.SSS')}Z`
    const utcStartTime = moment.utc(utcStart)
    const utcEndTime = moment.utc(utcEnd)
    utcStartTime.hours(sHour).minutes(sMinute)
    utcEndTime.hours(eHour).minutes(eMinute)
    utcStartTime.add(4, 'hours')
    if (event.value) {
      event.value.start = `${utcStartTime.format('YYYY-MM-DDTHH:mm:ss.SSS')}Z`
      event.value.end = `${utcEndTime.format('YYYY-MM-DDTHH:mm:ss.SSS')}Z`
    }
  } catch (e) {
    console.error(e)
  }
}
const onChageDate = (val: string, time: string) => {
  date.value = val
  startTime.value = time
  console.log(time)
}
const onChangeTime = (val: string) => {
  if (event.value) startTime.value = val
}
const onOpenDeleteModal = () => {
  emit('close', true)
}
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const filterFn = (val: string, update: any) => {
  update(() => {
    const needle = val.toLowerCase()
    options.value = stringOptions.value.filter((v) => v.toLowerCase().indexOf(needle) > -1)
  })
}

const openPatientProfile = () => {
  if (selectedPatientId.value) {
    const airtableUrl = `https://airtable.com/${MAIN_BASE_ID}/${CLIENT_TABLE_ID}/${selectedPatientId.value}`
    window.open(airtableUrl, '_blank')
  }
}
</script>
<style lang="scss">
.mmm-btn-appointement {
  &::before {
    border: 1px solid rgba($color: #000000, $alpha: 0.24);
  }
  .q-icon {
    font-size: 1.3em;
    color: grey;
  }
  &:hover {
    &::before {
      border: 1px solid #6abdff !important;
    }
  }
  &.active {
    &::before {
      border: 1px solid #6abdff !important;
    }
    background-color: #eef7ff !important;
  }
}
</style>
