<template>
  <q-card>
    <q-card-section class="row items-center">
      <q-avatar icon="delete_outline" color="red" text-color="white" />
      <span class="q-ml-sm">Are you sure you want to delete this appointment?</span>
    </q-card-section>

    <q-card-actions align="right">
      <q-btn flat label="Cancel" color="primary" v-close-popup />
      <q-btn
        flat
        label="Confirm"
        color="primary"
        @click="onDelete"
        :disable="disabled"
        :loading="disabled"
      />
    </q-card-actions>
  </q-card>
</template>

<script setup lang="ts">
import { ref, type PropType } from 'vue'
import { useStaffStore } from 'stores/staff.js'
import { useScheduleStore } from 'stores/schedule.js'
import type { EventType } from 'components/models.js'
const disabled = ref(false)
const scheduleStore = useScheduleStore()
const staffStore = useStaffStore()
const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  type: {
    type: String as PropType<EventType>,
    default: 'Appointment',
  },
})
const emit = defineEmits(['close'])
const onDelete = async () => {
  disabled.value = true
  try {
    if (props.type === 'Appointment') await scheduleStore.deteleAppointment(props.id)
    else await staffStore.deleteTimeoff(props.id)
    emit('close')
  } catch {
    disabled.value = false
  }
}
</script>
