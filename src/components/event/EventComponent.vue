<template>
  <div class="full-height event-area row items-center" :style="areaStyle">
    <div
      v-for="event in cancelledEvents"
      :key="event.id"
      class="col cancelled-box col-shrink"
      @click="onClickCancelled(event)"
    ></div>
    <div
      v-if="!non_event"
      class="full-height q-pa-none q-mb-xs event-box col"
      :style="boxStyle"
      @click="onClick"
    >
      <template v-if="event === 'Appointment'">
        <template v-if="status !== 'Cancelled'">
          <div class="full-width full-height row q-pr-xs">
            <div class="col q-pr-xs">
              <div class="text-body2 mmm-ellipsis">{{ label }}</div>
              <div class="mmm-ellipsis" style="font-size: 0.6rem; margin-top: 2px">
                {{ timeSlot }}
              </div>
            </div>
            <event-mark 
              :is-new-patient="isNewPatient" 
              :notes="notes" 
              :status="status" 
              :group="group" 
              :duration="duration" 
            />
          </div>
        </template>
      </template>
      <div v-else class="full-height row items-center justify-center">
        <div
          class="text-caption text-center bg-white rounded-borders q-px-sm text-grey-6"
          style="border: 1px solid #dddddd"
        >
          {{ label }}
        </div>
      </div>
    </div>
    <div v-else class="full-width full-height"></div>
    <q-dialog v-model="appointmentOpen" persistent>
      <template #default>
        <appointment-modal v-bind="appointmentValue" @close="onCloseEventModal" />
      </template>
    </q-dialog>
    <q-dialog v-model="deleteOpen" persistent>
      <template #default>
        <delete-modal v-bind="appointmentValue" @close="deleteOpen = false" />
      </template>
    </q-dialog>
  </div>
</template>

<script setup lang="ts">
import moment from 'moment'
import type { ProviderField, StatusType } from '../models'
import { computed, ref, onMounted } from 'vue'
import AppointmentModal from './AppointmentModal.vue'
import DeleteModal from './DeleteModal.vue'
import EventMark from "./EventMark.vue";
import { DEFAULT_PROVIDER_FIELD } from '../variable'
const props = withDefaults(defineProps<ProviderField>(), {
  name: 'Time Off',
  event: 'Time Off',
  start: '',
  end: '',
  duration: '',
  notes: '',
  appointment: 'Medical',
  patient: () => [],
  status: '' as StatusType,
  split: 1,
  non_event: false,
  pid: '',
  isNewPatient: false,
  group: '',
  cancelled: () => [],
})
const appointmentOpen = ref(false)
const deleteOpen = ref(false)
const startTime = ref('')
const endTime = ref('')
const cancelledEvents = computed(() => props.cancelled)
const appointmentValue = ref<ProviderField>(DEFAULT_PROVIDER_FIELD)
const boxStyle = computed(() => {
  if (props.event === 'Time Off') {
    return `background: repeating-linear-gradient(45deg, transparent, transparent 10px, #f2f2f2 10px, #f2f2f2 20px);`
  }
  let padding = 'padding-left: 2px'
  if (props.duration === '30 minutes') padding = 'padding-left: 4px; padding-top: 4px;'
  else if (props.duration === '60 minutes') padding = 'padding-left: 8px; padding-top: 8px;'
  let color = `background-color: #E4F3FF;${padding}` //pending
  if (props.status === 'Confirmed') color = `background-color: #F1F7EC;${padding}`
  else if (props.status === 'Complete') color = `background-color: #efefef;${padding}`
  else if (props.status === 'No Show') color = `background-color: #e0e0e0;${padding}`
  return color
})
const areaStyle = computed(() => {
  if (!props.non_event)
    return `
      border-left: 2px solid white;
      border-right: 2px solid white;
      border-bottom: 1px solid white;
      border-top: 1px solid white;
    `
  return ''
})

const isNewPatient = computed(() => props.isNewPatient)
const group = computed(() => props.group)
const label = computed(() => removeEmojis(props.name))
const timeSlot = computed(() => {
  const start = moment(props.start).format('hh:mm A')
  const end = moment(props.end).format('hh:mm A')
  return `${start} - ${end}`
})
onMounted(() => {
  startTime.value = moment(props.start).format('HH:mm')
  endTime.value = moment(props.end).format('HH:mm')
})
const removeEmojis = (str: string) => {
  // eslint-disable-next-line no-misleading-character-class
  return str.replace(/[\p{Emoji}\u200B-\u200D\uFE0F]/gu, '')
}
const onClick = () => {
  appointmentValue.value = props
  appointmentOpen.value = !appointmentOpen.value
}
const onClickCancelled = (event: ProviderField) => {
  appointmentValue.value = { ...event }
  appointmentOpen.value = !appointmentOpen.value
}
const onCloseEventModal = (isDelete: boolean) => {
  appointmentOpen.value = false
  deleteOpen.value = isDelete
}
</script>
<style lang="scss">
.event-area {
  .cancelled-box {
    cursor: pointer;
    background-color: #ffe4e4;
    border-radius: 20px;
    width: 12px !important;
    margin-right: 3px;
    height: 100%;
    &:hover {
      box-shadow:
        0 1px 5px #0003,
        0 2px 2px #00000024,
        0 3px 1px -2px #0000001f;
    }
  }
  .event-box {
    min-height: auto;
    z-index: 10001;
    cursor: pointer;
    border-radius: 4px;
    text-align: left;
    &:hover {
      box-shadow:
        0 1px 5px #0003,
        0 2px 2px #00000024,
        0 3px 1px -2px #0000001f;
    }
  }
}
</style>
