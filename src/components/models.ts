export interface Todo {
  id: number
  content: string
}

export interface Meta {
  totalCount: number
}

export interface ProviderInterface {
  id: string
  name: string
  fields: Provider<PERSON>ield[]
}
export interface ScheduleInterface {
  id: string
  createdTime: string
  fields: ScheduleField
}
export interface StaffInterface {
  id: string
  createdTime: string
  fields: <PERSON><PERSON><PERSON>
}
export interface ClientInterface {
  id: string
  createdTime: string
  fields: Client<PERSON>ield
}
export interface ProviderField {
  id: string
  name: string
  event: EventType
  start: string | Date
  end: string | Date
  duration: string
  notes: string
  appointment: AppointmentType
  patientName: string[]
  patientId: string[]
  status: StatusType
  split: number
  pid?: string
  non_event?: boolean
  isNewPatient?: boolean
  group?: string
  background?: boolean
  cancelled?: ProviderField[]
}
export interface ScheduleField {
  'Event Type': EventType
  Provider: string[]
  Date: string
  Duration: string
  Status: StatusType
  'Appointment Type': AppointmentType
  'Patient Name': string[]
  Notes: string
  Name: string
  'End Time': string
  'Patient Name (from Patient Name)': string[]
  'Last Modified': string
}
export interface ClientField {
  'Patient Name': string
  'Last Visit Date': string
  'Last Modified Time': string
}
export interface StaffField {
  Appointments: string
  'Default Working Hours': string
  Role: RoleType
  Schedule: string[]
  'Staff Member': string
  'Time Off': string
  'Start Date': string
  'Inactive Date': string
  'Last Modified': string
}
export interface UserInterface {
  email: string
  password: string
  role: UserRoleType
  name: string
}
export type EventType = 'Appointment' | 'Time Off'
export type StatusType =
  | 'Pending'
  | 'Confirmed'
  | 'Complete'
  | 'No Show'
  | 'Cancelled'
  | 'Rescheduled'
  | 'Archived'
export type AppointmentType = 'Medical' | 'Massage' | 'Acupuncture'
export type RoleType = 'NP' | 'PA' | 'Acupuncturist' | 'Massage Therapist'
export type UserRoleType = 'Admin' | 'Staff' | 'Patient'
export interface TimeValue {
  hours: number
  minutes: number
  period: 'AM' | 'PM'
}

export interface PickerItem {
  value: number
  label: string
}

export interface PickerConfig {
  range: number
  width: string
}

export interface ScrollRef {
  getScrollPosition: () => number
  setScrollPosition: (direction: 'vertical', position: number, duration: number) => void
}
export interface YearMonth {
  year: number
  month: number
}
export type WeekDay =
  | 'Sunday'
  | 'Monday'
  | 'Tuesday'
  | 'Wednesday'
  | 'Thursday'
  | 'Friday'
  | 'Saturday'

export const WEEKDAYS: WeekDay[] = [
  'Sunday',
  'Monday',
  'Tuesday',
  'Wednesday',
  'Thursday',
  'Friday',
  'Saturday',
]
