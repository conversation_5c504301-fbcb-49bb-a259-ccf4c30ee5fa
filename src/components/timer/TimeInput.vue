<template>
  <q-select
    outlined
    dense
    v-model="time"
    :options="options"
    :label="label"
    :rules="[(val) => (val && val.length > 0) || 'Please type something']"
    :hide-bottom-space="false"
    :readonly="readOnly"
    @update:model-value="onEmit"
  />
</template>

<script setup lang="ts">
import moment from 'moment'
import { computed, onMounted, ref, watch } from 'vue'
//import { storeToRefs } from 'pinia'
import { generateTimeList } from 'src/utils'
const props = defineProps({
  label: {
    type: String,
  },
  value: {
    type: String,
  },
})
const emit = defineEmits(['change'])
const options = ref<string[]>([])
const readOnly = computed(() => false)
const time = ref('08:00 AM')
const monitorValue = computed(() => props.value || '')
onMounted(() => {
  const { time: _time, options: _options } = generateTimeList()
  if (props.value) {
    time.value = moment(props.value, 'HH:mm').format('hh:mm A')
  } else {
    time.value = _time
  }
  options.value = _options
})

const onEmit = () => {
  const _time = moment(time.value, 'hh:mm A').format('HH:mm')
  emit('change', _time)
}
watch(monitorValue, (newValue: string) => {
  time.value = moment(newValue, 'HH:mm').format('hh:mm A')
})
</script>
