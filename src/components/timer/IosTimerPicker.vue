<template>
  <div @click="onfocus" class="ios-virtual-box">
    <q-input
      outlined
      dense
      v-model="time"
      readonly
      :label="props.label"
      ref="picker"
      class="q-mb-md"
    >
    </q-input>
  </div>
  <q-dialog v-model="open" position="bottom" persistent>
    <q-card style="width: 350px">
      <div class="row align-center q-px-sm q-py-xs">
        <q-btn label="Cancel" no-caps flat :ripple="false" @click="onClose" />
        <q-space />
        <q-btn label="Done" no-caps color="primary" flat :ripple="false" @click="onEmit" />
      </div>
      <div class="my-ios-time-picker bg-white">
        <!-- Hours Picker -->
        <vue-scroll-picker
          :options="hours"
          v-model="selectedHour"
          item-height="80"
          height="150"
          class="picker-column"
        />

        <!-- Minutes Picker -->
        <vue-scroll-picker
          :options="minutes"
          v-model="selectedMinute"
          item-height="80"
          height="150"
          class="picker-column"
        />

        <!-- AM/PM Picker -->
        <vue-scroll-picker
          :options="periods"
          v-model="selectedPeriod"
          item-height="80"
          height="150"
          class="picker-column"
        />
        <!-- Display Selected Time -->
      </div>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import moment from 'moment'
import { computed, ref, onMounted, watch } from 'vue'
import { VueScrollPicker } from 'vue-scroll-picker'
import 'vue-scroll-picker/lib/style.css'
// Generate picker options using TypeScript
const hours = Array.from({ length: 12 }, (_, i) => (i + 1).toString().padStart(2, '0')) // 01-12
const minutes = Array.from({ length: 60 }, (_, i) => i.toString().padStart(2, '0')) // 00-59
const periods = ['AM', 'PM'] // AM/PM options

const props = defineProps({
  label: {
    type: String,
    default: 'Start Time',
  },
  value: {
    type: String,
  },
})
const emit = defineEmits(['change'])
const picker = ref()
const open = ref(false)
// Define types for reactive states
const selectedHour = ref('08')
const selectedMinute = ref('30')
const selectedPeriod = ref('AM')
const _time = ref()
const time = computed(() => `${selectedHour.value}:${selectedMinute.value} ${selectedPeriod.value}`)
const monitorValue = computed(() => props.value || '')
onMounted(() => {
  onInit(props.value, true)
})
const onfocus = () => {
  open.value = true
}
const onInit = (value: string | Date | undefined, updated: boolean) => {
  if (value) {
    selectedPeriod.value = moment(value, 'HH:mm').format('A')
    const hh_mm = moment(value, 'HH:mm').format('hh:mm')
    const arr = hh_mm.split(':')
    selectedHour.value = arr[0] || ''
    selectedMinute.value = arr[1] || ''
  }
  if (updated) {
    _time.value = `${selectedHour.value}:${selectedMinute.value} ${selectedPeriod.value}`
  }
}
const onClose = () => {
  onInit(_time.value, false)
  open.value = false
}
const onEmit = () => {
  open.value = false
  const _time = moment(time.value, 'hh:mm A').format('HH:mm')
  onInit(_time, true)
  emit('change', _time)
}
watch(monitorValue, (newValue: string) => {
  onInit(newValue, true)
})
</script>
<style lang="scss" scoped>
.ios-virtual-box {
  :deep(.q-field__control:before) {
    border-style: groove;
  }
}
/* Main picker container */
.my-ios-time-picker {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  padding: 20px;
  max-width: 400px;
  margin: 0 auto;
  background: #f7f7f7;

  /* Picker columns */
  .picker-column {
    flex: 1;
    text-align: center;
    position: relative;
    overflow: hidden;
    height: 150px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  /* Highlight effect for the picker */
  .picker-column::before,
  .picker-column::after {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    height: 50px; /* Matches item height */
    // background: rgba(0, 0, 0, 0.025);
    z-index: 1;
    pointer-events: none;
  }

  .picker-column::before {
    top: 0;
  }

  .picker-column::after {
    bottom: 0;
  }

  /* Active item styling */
  :deep(.vue-scroll-picker-item-selected) {
    font-size: 22px;
    color: gray;
    font-weight: bold;
    text-transform: uppercase;
    transform: scale(1.1);
    transition:
      transform 0.3s,
      color 0.3s ease-in-out;
  }
  /* Inactive items */
  :deep(.vue-scroll-picker-item) {
    font-size: 22px;
    opacity: 0.6;
    transition:
      opacity 0.3s,
      font-size 0.3s ease-in-out;
  }

  /* Time display below picker */
  .time-display {
    margin-top: 20px;
    text-align: center;
    font-size: 1.6rem;
    font-weight: 600;
    color: #333;
  }

  /* Responsive design */
  @media (max-width: 600px) {
    .ios-time-picker {
      padding: 15px;
      gap: 10px;
    }

    .vue-scroll-picker-item-selected {
      font-size: 18px;
    }

    .vue-scroll-picker-item {
      font-size: 14px;
    }

    .time-display {
      font-size: 1.2rem;
    }
  }
}
</style>
