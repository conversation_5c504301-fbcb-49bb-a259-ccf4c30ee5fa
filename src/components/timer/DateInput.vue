<template>
  <div>
    <q-input
      :class="className"
      outlined
      dense
      v-model="mDate"
      label="Date"
      locale="en-us"
      :readonly="true"
      @click="onClick"
      class="q-mb-lg"
    >
      <template v-slot:append>
        <q-btn round flat size="sm" icon="event" @click="onClick" />
      </template>
    </q-input>
    <q-dialog v-model="open">
      <q-card class="mma-calendar-box">
        <q-card-section>
          <div class="row q-px-lg q-pt-md items-center">
            <div class="text-body1">
              {{ displayDate }}
            </div>
            <q-space />
            <q-btn
              flat
              class="q-mr-sm"
              icon="arrow_back_ios"
              round
              size="xs"
              @click="onPrevMonth"
            />
            <q-btn flat icon="arrow_forward_ios" round size="xs" @click="onNextMonth" />
          </div>
          <q-date
            v-model="mDate"
            class="full-width q-mb-md"
            :format="US_TIME_FORMAT"
            :mask="US_TIME_FORMAT"
            minimal
            flat
            no-unset
          >
          </q-date>
          <hr style="margin-top: -16px" />
          <div class="row q-pr-md items-center q-pt-sm q-pl-md">
            <div class="row q-px-md q-py-sm items-center full-width action-btn" @click="onToday">
              <div class="text-body1">Today</div>
              <q-space />
              <div class="text-body2 text-grey">{{ moment().format('ddd, MMM DD') }}</div>
            </div>
            <div
              class="row q-mt-md q-px-md q-py-sm items-center full-width action-btn"
              @click="onTomorrow"
            >
              <div class="text-body1">Tomorrow</div>
              <q-space />
              <div class="text-body2 text-grey">
                {{ moment().add(1, 'day').format('ddd, MMM DD') }}
              </div>
            </div>
            <div
              class="row q-mt-md q-px-md q-py-sm items-center full-width action-btn"
              @click="onNextWeek"
            >
              <div class="text-body1">Next Week</div>
              <q-space />
              <div class="text-body2 text-grey">
                {{ moment().add(1, 'week').format('ddd, MMM DD') }}
              </div>
            </div>
          </div>
          <hr class="q-mt-lg" />
          <div class="row q-pr-md q-pl-lg q-pt-md">
            <div class="q-pt-sm">
              <q-icon class="" name="schedule" size="1.5em" />
              <span class="text-body2 q-ml-sm">Time</span>
            </div>
            <q-space />
            <time-input :value="time" @change="onChangeTime" />
          </div>
        </q-card-section>
        <q-card-actions align="right">
          <q-btn flat label="Cancel" color="primary" @click="onSave(false)" v-close-popup />
          <q-btn flat label="OK" color="primary" @click="onSave(true)" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup lang="ts">
import moment from 'moment'
import { computed, onMounted, ref } from 'vue'
import { US_TIME_FORMAT } from '../variable'
import { generateTimeList } from 'src/utils'
import TimeInput from './TimeInput.vue'
const props = defineProps({
  date: {
    type: String,
    default: moment().format(US_TIME_FORMAT),
  },
  currentTime: {
    //HH:mm
    type: String,
  },
  cover: {
    type: Boolean,
    default: false,
  },
})
const emits = defineEmits(['change'])
const mDate = ref(moment().format(US_TIME_FORMAT))
const displayDate = computed(() => moment(mDate.value, US_TIME_FORMAT).format('MMMM YYYY'))
const open = ref(false)
const time = ref('08:00 AM')
const className = computed(() => (!props.cover ? 'mma-date-input' : ''))
onMounted(() => {
  mDate.value = props.date
  if (!props.currentTime) {
    const { time: _time } = generateTimeList()
    time.value = _time
  } else {
    time.value = props.currentTime
  }
})

const onClick = () => {
  if (props.cover) return
  open.value = !open.value
}
const onSave = (flag: boolean) => {
  if (flag) emits('change', mDate.value, moment(time.value, 'hh:mm A').format('HH:mm'))
  else mDate.value = props.date
}
const onChangeTime = (val: string) => {
  time.value = val
}
const onNextMonth = () => {
  mDate.value = moment(mDate.value, US_TIME_FORMAT).add(1, 'month').format(US_TIME_FORMAT)
}
const onPrevMonth = () => {
  mDate.value = moment(mDate.value, US_TIME_FORMAT).subtract(1, 'month').format(US_TIME_FORMAT)
}
const onToday = () => {
  mDate.value = moment().format(US_TIME_FORMAT)
}
const onTomorrow = () => {
  mDate.value = moment().add(1, 'day').format(US_TIME_FORMAT)
}
const onNextWeek = () => {
  mDate.value = moment().add(1, 'week').format(US_TIME_FORMAT)
}
</script>
<style lang="scss">
.mma-date-input {
  .q-field__control::before {
    border-style: solid !important;
  }
}
.mma-calendar-box {
  max-width: 340px !important;
  .q-date__calendar-item {
    opacity: 1;
    .q-btn {
      width: 34px;
      height: 34px;
      color: black !important;
      border-radius: 10px;
      &.q-date__today {
        box-shadow: none;
      }
      &.bg-primary {
        background-color: #dbeafc !important;
        border: 2px solid #6abdff;
      }
    }
  }
  .action-btn {
    background-color: #e9eef6;
    border-radius: 8px;
    cursor: pointer;
    border: 1px solid #e9eef6;
    &:hover {
      border: 1px solid #6abdff;
    }
  }
}
</style>
