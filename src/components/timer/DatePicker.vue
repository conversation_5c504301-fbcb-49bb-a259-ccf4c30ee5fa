<template>
  <q-card class="bg-grey-2 no-shadow no-border q-pa-none">
    <div class="q-pt-md row items-center">
      <div class="text-body1 q-py-xs q-pl-sm">
        {{ moment(date, US_TIME_FORMAT).format('MMMM YYYY') }}
      </div>
      <q-space />
      <q-btn
        icon="arrow_back_ios"
        size="sm"
        dense
        color="grey-9"
        flat
        round
        :ripple="false"
        @click="onPrev"
      />
      <q-btn
        class="q-ml-sm"
        icon="arrow_forward_ios"
        color="grey-9"
        flat
        size="sm"
        round
        :ripple="false"
        dense
        @click="onNext"
      />
    </div>
    <q-date
      v-model="date"
      class="full-width bg-grey-2 q-pa-none mma-date-picker"
      :format="US_TIME_FORMAT"
      :mask="US_TIME_FORMAT"
      flat
      minimal
      no-unset
      :events="_events"
    >
    </q-date>
  </q-card>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { US_TIME_FORMAT } from '../variable'
import { storeToRefs } from 'pinia'
import { useFilterStore } from 'stores/filter'
import { useScheduleStore } from 'stores/schedule'
import moment from 'moment'
import { useCenterStore } from 'stores/center'
const scheduleStore = useScheduleStore()
const centerStre = useCenterStore()
const filterStore = useFilterStore()
const { _date, patientName } = storeToRefs(filterStore)
const { providers } = storeToRefs(centerStre)
const date = computed({
  get: () => _date.value,
  set: (val) => filterStore.changeDate(val),
})
const onNext = async () => {
  const nextMoment = moment(_date.value, US_TIME_FORMAT).add(1, 'month')
  const hasSchedule = scheduleStore.hasSchedule(nextMoment)
  filterStore.changeDate(nextMoment.format(US_TIME_FORMAT))
  if (!hasSchedule) {
    filterStore.loading = true
    await scheduleStore.getSchedules()
  }
  filterStore.loading = false
}
const _events = computed(() => {
  //if (!patientName.value) return undefined

  const events: string[] = []
  providers.value.forEach((provider) => {
    provider.fields.forEach((field) => {
      const name = field.patientName[0] || ''
      if (name.toLowerCase().includes(patientName.value.toLowerCase())) {
        if (
          field.event === 'Appointment' &&
          field.status !== 'Cancelled' &&
          field.status !== 'Archived'
        ) {
          const mStart = moment(field.start, 'YYYY-MM-DD HH:mm')
          const mDate = moment(_date.value, US_TIME_FORMAT)
          if (mStart.isSame(mDate, 'month')) {
            events.push(mStart.format('YYYY/MM/DD'))
          }
        }
      }
    })
  })
  return events
})
const onPrev = async () => {
  const prevMoment = moment(_date.value, US_TIME_FORMAT).subtract(1, 'month')
  const hasSchedule = scheduleStore.hasSchedule(prevMoment)
  filterStore.changeDate(prevMoment.format(US_TIME_FORMAT))
  if (!hasSchedule) {
    filterStore.loading = true
    await scheduleStore.getSchedules()
  }
  filterStore.loading = false
}
</script>
<style lang="scss">
.mma-date-picker {
  .q-date__view {
    min-height: 240px;
    padding-left: 0;
    padding-right: 0;
    padding-top: 8px;
    .q-date__calendar-item {
      opacity: 1;
      .q-btn {
        width: 34px;
        height: 34px;
        color: black !important;
        border-radius: 10px;
        &.q-date__today {
          box-shadow: none;
        }
        &.bg-primary {
          background-color: #dbeafc !important;
          border: 2px solid #6abdff;
        }
      }
    }
  }
}
</style>
