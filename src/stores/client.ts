import { defineStore, acceptHMRUpdate } from 'pinia'
import { CLIENT_TABLE_ID, CLIENT_VIEW_ID, MAIN_BASE_ID } from 'src/components/variable'
import { useScheduleStore } from './schedule.js'
import axios from 'axios'
import type { ClientInterface } from 'src/components/models'
import { getLastedDate } from 'src/utils'

const getAll = async (params: { view: string; filterByFormula: string }) => {
  let offset: string | undefined
  let clients: ClientInterface[] = []
  do {
    try {
      const paramsWithOffset = offset ? { ...params, offset } : params
      const response = await axios.get(`/${MAIN_BASE_ID}/${CLIENT_TABLE_ID}`, {
        params: paramsWithOffset,
      })
      const records = response.data.records as ClientInterface[]
      clients = clients.concat(records)
      offset = response.data.offset
    } catch (err) {
      console.error(err)
      break
    }
  } while (offset)
  return clients
}
export const useClientStore = defineStore('client', {
  state: () => ({
    clients: [] as ClientInterface[],
    loading: false,
  }),

  getters: {
    _clients: (state) => state.clients,
  },

  actions: {
    async updateClient(id: string, name: string) {
      const scheduleStore = useScheduleStore()
      const res = await axios.patch(
        `/${MAIN_BASE_ID}/${CLIENT_TABLE_ID}/${id}`,
        {
          fields: {
            'Patient Name': name,
          },
        },
        {
          headers: {
            'Content-Type': 'application/json',
          },
          params: {
            view: CLIENT_VIEW_ID,
          },
        },
      )
      try {
        const newData = res.data
        this.clients = this.clients.map((el) => {
          if (el.id === newData.id) return { ...newData }
          return el
        })
        const name = newData.fields['Patient Name']
        scheduleStore.changePatient(id, name)
      } catch {
        console.error('No credentials')
      }
    },
    async getUpdates() {
      if (this.loading) return
      this.loading = true
      const latestTime = getLastedDate(this.clients.map((el) => el.fields['Last Modified Time']))
      const params = {
        view: CLIENT_VIEW_ID,
        filterByFormula: `AND({Patient Name} != '', IS_AFTER({Last Modified Time}, '${latestTime}'))`,
      }
      try {
        let records = await getAll(params)
        records = records.filter((el) => el.fields['Patient Name'])
        if (records.length > 0) {
          const clientIds = this.clients.map((el) => el.id)
          const newRecords = records.filter((el) => !clientIds.includes(el.id))
          this.clients = this.clients
            .map((el) => {
              const found = records.find((record) => record.id === el.id)
              if (found) return { ...found }
              return el
            })
            .concat(newRecords)
        }
      } catch {
        /** */
      }
      this.loading = false
    },
    async getClients() {
      const params = {
        view: CLIENT_VIEW_ID,
        filterByFormula: `{Patient Name} != ''`,
      }
      let records = await getAll(params)
      records = records.filter((el) => el.fields['Patient Name'].trim())
      this.clients = records
    },
  },
})

if (import.meta.hot) {
  import.meta.hot.accept(acceptHMRUpdate(useClientStore, import.meta.hot))
}
