import { defineStore, acceptHMRUpdate } from 'pinia'
import axios from 'axios'
import {
  MAIN_BASE_ID,
  SCHEDULE_TABLE_ID,
  SCHEDULE_VIEW_ID,
  US_TIME_FORMAT,
} from '../components/variable'
import type { ProviderField, ScheduleInterface } from 'src/components/models'
import { getLastedDate } from 'src/utils'
import { useFilterStore } from './filter'
import { useAuthStore } from './user'
import type { Moment } from 'moment'
import moment from 'moment'

const getAll = async (params: { view: string; filterByFormula: string }) => {
  let offset: string | undefined
  let schedules: ScheduleInterface[] = []
  do {
    try {
      const paramsWithOffset = offset ? { ...params, offset } : params
      const response = await axios.get(`/${MAIN_BASE_ID}/${SCHEDULE_TABLE_ID}`, {
        params: paramsWithOffset,
      })
      const records = response.data.records as ScheduleInterface[]
      schedules = schedules.concat(records)
      offset = response.data.offset
    } catch (err) {
      console.error(err)
      break
    }
  } while (offset)
  return schedules
}
export const useScheduleStore = defineStore('schedule', {
  state: () => ({
    schedules: [] as ScheduleInterface[],
    //   records: [] as ScheduleInterface[],
    loading: false,
  }),

  getters: {},

  actions: {
    init() {
      this.schedules = []
    },
    async getUpdates() {
      if (this.loading) return
      const userStore = useAuthStore()
      this.loading = true
      const latestTime = getLastedDate(this.schedules.map((el) => el.fields['Last Modified']))
      const query = userStore.getQuery()
      let params = {
        view: SCHEDULE_VIEW_ID,
        filterByFormula: `AND(Status != 'Archived', IS_AFTER({Last Modified}, '${latestTime}'))`,
      }
      if (query) {
        params = {
          ...params,
          filterByFormula: `AND(Status != 'Archived', IS_AFTER({Last Modified}, '${latestTime}'), ${query})`,
        }
      }
      try {
        const records = await getAll(params)
        if (records.length > 0) {
          const scheduleIds = this.schedules.map((el) => el.id)
          const newRecords = records.filter((el) => !scheduleIds.includes(el.id))
          this.schedules = this.schedules
            .map((el) => {
              const found = records.find((record) => record.id === el.id)
              if (found) return { ...found }
              return el
            })
            .concat(newRecords)
        }
      } catch {
        /** */
      }
      this.loading = false
    },
    async getSchedules() {
      const filterStore = useFilterStore()
      const userStore = useAuthStore()
      const { _date } = filterStore
      const month = moment(_date, US_TIME_FORMAT).month() + 1
      const year = moment(_date, US_TIME_FORMAT).year()
      const query = userStore.getQuery()
      let params = {
        view: SCHEDULE_VIEW_ID,
        filterByFormula: `AND(Status != 'Archived', MONTH(Date) = ${month}, YEAR(Date) = ${year})`,
      }
      if (query) {
        params = {
          ...params,
          filterByFormula: `AND(Status != 'Archived', MONTH(Date) = ${month}, YEAR(Date) = ${year}, ${query})`,
        }
      }
      try {
        const records = await getAll(params)
        if (records.length > 0) {
          const newRecords = records.filter(
            (record) => !this.schedules.some((el) => el.id === record.id),
          )
          this.schedules = this.schedules
            .map((schedule) => {
              const record = records.find((record) => record.id === schedule.id)
              if (!record) return schedule
              return record
            })
            .concat(newRecords)
          this.schedules = this.schedules.filter((el) => el.fields.Status !== 'Archived')
        }
      } catch {
        /* */
      }
    },
    async coverSchedule(pid: string, id: string) {
      if (pid && id) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const schedule: any = {
          Provider: [pid],
        }
        const response = await axios.patch(
          `/${MAIN_BASE_ID}/${SCHEDULE_TABLE_ID}/${id}`,
          {
            fields: {
              ...schedule,
            },
          },
          {
            headers: {
              'Content-Type': 'application/json',
            },
            params: {
              view: SCHEDULE_VIEW_ID,
            },
          },
        )
        const newSchedule = response.data as ScheduleInterface
        this.schedules = this.schedules.map((el) => {
          if (el.id === id) return newSchedule
          return el
        })
      }
    },
    async updateSchedule(pid: string, field?: ProviderField) {
      if (field) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const schedule: any = {
          'Appointment Type': field.appointment,
          Date: field.start as string,
          Duration: field.duration,
          Provider: [pid],
        }
        if (field.event === 'Appointment') {
          schedule['Appointment Type'] = field.appointment
          schedule['Notes'] = field.notes
          schedule['Status'] = field.status
        }
        const response = await axios.patch(
          `/${MAIN_BASE_ID}/${SCHEDULE_TABLE_ID}/${field.id}`,
          {
            fields: {
              ...schedule,
            },
          },
          {
            headers: {
              'Content-Type': 'application/json',
            },
            params: {
              view: SCHEDULE_VIEW_ID,
            },
          },
        )
        const newSchedule = response.data as ScheduleInterface
        if (newSchedule.fields['Status'] === 'Cancelled') {
          this.schedules = this.schedules.filter((el) => el.id !== newSchedule.id)
        } else {
          this.schedules = this.schedules.map((el) => {
            if (el.id === field.id) {
              return newSchedule
            }
            return el
          })
        }
      }
      this.loading = false
    },
    async changePatient(id: string, name: string) {
      this.schedules = this.schedules.map((el) => {
        const patiendId = el.fields['Patient Name'][0]
        if (patiendId === id) {
          return {
            ...el,
            fields: {
              ...el.fields,
              'Patient Name (from Patient Name)': [name],
            },
          }
        }
        return el
      })
    },
    async createNewAppointement(providerId: string, field: ProviderField) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const schedule: any = {
        Date: field.start,
        Duration: field.duration,
        'Event Type': field.event,
        Provider: [providerId],
        Status: 'Pending',
      }
      if (field.event === 'Appointment') {
        schedule['Appointment Type'] = field.appointment
        schedule['Notes'] = field.notes
        schedule['Patient Name'] = field.patientId
        schedule['Status'] = field.status
      }
      const response = await axios.post(
        `/${MAIN_BASE_ID}/${SCHEDULE_TABLE_ID}`,
        {
          fields: {
            ...schedule,
          },
        },
        {
          headers: {
            'Content-Type': 'application/json',
          },
          params: {
            view: SCHEDULE_VIEW_ID,
          },
        },
      )
      this.schedules.push(response.data)
    },
    async deteleAppointment(id: string) {
      if (!id) return
      const schedule = {
        Status: 'Archived',
      }
      await axios.patch(
        `/${MAIN_BASE_ID}/${SCHEDULE_TABLE_ID}/${id}`,
        {
          fields: {
            ...schedule,
          },
        },
        {
          headers: {
            'Content-Type': 'application/json',
          },
          params: {
            view: SCHEDULE_VIEW_ID,
          },
        },
      )
      this.schedules = this.schedules.filter((el) => el.id !== id)
    },
    async getSchedulesFromDateRange(start: Date, end: Date) {
      const params = {
        view: SCHEDULE_VIEW_ID,
        filterByFormula: `AND(Status = 'Pending', IS_AFTER({Date}, '${start}'), IS_BEFORE({Date}, '${end}'))`,
      }
      try {
        const records = await getAll(params)
        return records
      } catch {
        /** */
      }
      return []
    },
    async setConfirm(recordId: string) {
      const response = await axios.patch(
        `/${MAIN_BASE_ID}/${SCHEDULE_TABLE_ID}/${recordId}`,
        {
          fields: {
            Status: 'Confirmed',
          },
        },
        {
          headers: {
            'Content-Type': 'application/json',
          },
          params: {
            view: SCHEDULE_VIEW_ID,
          },
        },
      )
      const newSchedule = response.data as ScheduleInterface
      return newSchedule
    },
    hasSchedule(param: Moment) {
      const paramMonth = param.month() + 1
      const paramYear = param.year()
      return this.schedules.some((schedule) => {
        const scheduleM = moment.utc(schedule.fields.Date).subtract(4, 'hours')
        const year = scheduleM.year()
        const month = scheduleM.month() + 1
        return paramYear === year && paramMonth === month
      })
    },
  },
})

if (import.meta.hot) {
  import.meta.hot.accept(acceptHMRUpdate(useScheduleStore, import.meta.hot))
}
