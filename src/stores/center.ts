import { defineStore, acceptHMRUpdate } from 'pinia'
import { useScheduleStore } from 'stores/schedule'
import { useStaffStore } from 'stores/staff'
import { useAuthStore } from 'stores/user'
import moment from 'moment'
import type { ProviderField } from 'src/components/models'
import { useFilterStore } from './filter'
import { parseWorkingHours } from 'src/utils'
import { MMM_CRED } from 'src/components/variable'
export const useCenterStore = defineStore('center', {
  state: () => ({}),
  getters: {
    providers: () => {
      const scheduleStore = useScheduleStore()
      const staffStore = useStaffStore()
      const schedules = scheduleStore.schedules
      const staffs = staffStore.staffs
      const providers = staffs.map((staff) => {
        const id = staff.id
        const name = staff.fields['Staff Member']
        const fields = schedules
          .filter((schedule) => {
            const _providers = schedule.fields.Provider || []
            return _providers.includes(id)
          })
          .map(
            (schedule) =>
              ({
                id: schedule.id,
                name: schedule.fields.Name,
                event: schedule.fields['Event Type'],
                start: moment
                  .utc(schedule.fields.Date)
                  .subtract(4, 'hours')
                  .format('YYYY-MM-DD HH:mm'),
                end: moment
                  .utc(schedule.fields['End Time'])
                  .subtract(4, 'hours')
                  .format('YYYY-MM-DD HH:mm'),
                duration: schedule.fields.Duration,
                notes: schedule.fields.Notes,
                appointment: schedule.fields['Appointment Type'],
                patientName: schedule.fields['Patient Name (from Patient Name)'] || [],
                patientId: schedule.fields['Patient Name'] || [],
                status: schedule.fields.Status,
                split: 1,
              }) as ProviderField,
          )
        return {
          id,
          name,
          fields: fields,
          workingDays: parseWorkingHours(staff.fields['Default Working Hours']),
          role: staff.fields.Role,
          startDate: staff.fields['Start Date'],
          inactiveDate: staff.fields['Inactive Date'],
        }
      })
      return providers
    },
  },
  actions: {
    init() {
      localStorage.removeItem(MMM_CRED)
      const scheduleStore = useScheduleStore()
      const staffStore = useStaffStore()
      const filterStore = useFilterStore()
      const authStore = useAuthStore()
      scheduleStore.init()
      staffStore.init()
      filterStore.init()
      authStore.init()
    },
  },
})

if (import.meta.hot) {
  import.meta.hot.accept(acceptHMRUpdate(useCenterStore, import.meta.hot))
}
