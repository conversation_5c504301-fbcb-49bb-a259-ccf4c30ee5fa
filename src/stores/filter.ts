import moment from 'moment'
import { defineStore, acceptHMRUpdate } from 'pinia'
import type { AppointmentType, EventType, YearMonth } from 'src/components/models'
import { US_TIME_FORMAT } from 'src/components/variable'
import { useScheduleStore } from 'stores/schedule.js'
export const useFilterStore = defineStore('filter', {
  state: () => ({
    counter: 0,
    date: moment().format(US_TIME_FORMAT),
    providerId: '',
    eventType: '' as EventType,
    patientName: '',
    appointmentType: '' as AppointmentType,
    open: true,
    loading: false,
    month: moment().month() + 1,
    year: moment().year(),
  }),

  getters: {
    _date: (state) => state.date,
    _providerId: (state) => state.providerId,
    _eventType: (state) => state.eventType,
    _patientName: (state) => state.patientName,
    _open: (state) => state.open,
    _loading: (state) => state.loading,
  },

  actions: {
    init() {
      this.date = moment().format(US_TIME_FORMAT)
      this.providerId = ''
      this.eventType = '' as EventType
      this.patientName = ''
      this.appointmentType = '' as AppointmentType
    },
    async changeDate(_date: string) {
      this.date = _date
    },
    updateProvider(id: string) {
      this.providerId = id
    },
    updateEvent(event: EventType) {
      this.eventType = event
    },
    updateAppointment(data: AppointmentType) {
      this.appointmentType = data
    },
    changePatient(patient: string) {
      this.patientName = patient
    },
    setOpen() {
      this.open = !this.open
    },
    setLoading(flag: boolean) {
      this.loading = flag
    },
    async setMonthYear(ym: YearMonth) {
      this.month = ym.month
      this.year = ym.year
      const store = useScheduleStore()
      await store.getSchedules()
    },
  },
})

if (import.meta.hot) {
  import.meta.hot.accept(acceptHMRUpdate(useFilterStore, import.meta.hot))
}
