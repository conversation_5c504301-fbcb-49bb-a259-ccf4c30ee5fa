import { defineStore, acceptHMRUpdate } from 'pinia'
import axios from 'axios'
import { MAIN_BASE_ID, STAFF_VIEW_ID, STAFF_TABLE_ID } from '../components/variable'
import type { StaffInterface } from 'src/components/models'
import { getLastedDate } from 'src/utils'

const getAll = async (params: { view: string; filterByFormula: string }) => {
  let offset: string | undefined
  let providers: StaffInterface[] = []
  do {
    try {
      const paramsWithOffset = offset ? { ...params, offset } : params
      const response = await axios.get(`/${MAIN_BASE_ID}/${STAFF_TABLE_ID}`, {
        params: paramsWithOffset,
      })
      let records = response.data.records as StaffInterface[]
      records = records.filter((el) => {
        if (!el.fields) return false
        if (Object.keys(el.fields).length === 0) return false
        return true
      })
      providers = providers.concat(records)
      offset = response.data.offset
    } catch (err) {
      console.error(err)
      break
    }
  } while (offset)
  return providers
}

export const useStaffStore = defineStore('staff', {
  state: () => ({
    staffs: [] as StaffInterface[],
    loading: false,
  }),

  getters: {
    _staffs: (state) =>
      state.staffs.map((el) => ({
        id: el.id,
        name: el.fields['Staff Member'],
      })),
  },

  actions: {
    async init() {
      this.staffs = []
    },
    async login(uid: string) {
      const params = {
        view: STAFF_VIEW_ID,
        filterByFormula: `uid = '${uid}'`,
      }
      const response = await axios.get(`/${MAIN_BASE_ID}/${STAFF_TABLE_ID}`, {
        params,
      })
      const records = response.data ? response.data.records : []
      if (records.length > 0) {
        const data: StaffInterface = records[0]
        return {
          id: data.id,
          name: data.fields['Staff Member'],
          role: data.fields.Role,
        }
      }
      return null
    },
    async getUpdates() {
      if (this.loading) return
      this.loading = true
      const params = {
        view: STAFF_VIEW_ID,
        filterByFormula: '',
      }
      const latestTime = getLastedDate(this.staffs.map((el) => el.fields['Last Modified']))
      params['filterByFormula'] = `AND(IS_AFTER({Last Modified}, '${latestTime}'))`
      try {
        const records = await getAll(params)
        if (records.length > 0) {
          const staffIds = this.staffs.map((el) => el.id)
          const newRecords = records.filter((el) => !staffIds.includes(el.id))
          this.staffs = this.staffs
            .map((el) => {
              const found = records.find((record) => record.id === el.id)
              if (found) return { ...found }
              return el
            })
            .concat(newRecords)
        }
      } catch {
        /** */
      }
      this.loading = false
    },
    async getStaffs() {
      const params = {
        view: STAFF_VIEW_ID,
        filterByFormula: '',
      }
      try {
        const records = await getAll(params)
        this.staffs = records
      } catch {
        /** */
      }
      this.loading = false
    },
    async updateTimeoff(id: string, time: string) {
      if (!time) return
      const staff = this._staffs.find((el) => el.id === id)
      if (!staff) return
      const timeOff = `${staff.name}: ${time}`
      const response = await axios.patch(
        `/${MAIN_BASE_ID}/${STAFF_TABLE_ID}/${id}`,
        {
          fields: {
            'Time Off': timeOff,
          },
        },
        {
          headers: {
            'Content-Type': 'application/json',
          },
          params: {
            view: STAFF_VIEW_ID,
          },
        },
      )
      const newData = response.data
      this.staffs = this.staffs.map((el) => {
        if (el.id === id) return newData
        return el
      })
    },
    async createAppointment(id: string, appointment: string) {
      if (!appointment) return
      const staff = this.staffs.find((el) => el.id === id)
      if (!staff) return
    },
    async deleteTimeoff(id: string) {
      const staff = this.staffs.find((el) => el.id === id)
      if (!staff) return
      const response = await axios.patch(
        `/${MAIN_BASE_ID}/${STAFF_TABLE_ID}/${id}`,
        {
          fields: {
            'Time Off': '',
          },
        },
        {
          headers: {
            'Content-Type': 'application/json',
          },
          params: {
            view: STAFF_VIEW_ID,
          },
        },
      )
      const newData = response.data
      this.staffs = this.staffs.map((el) => {
        if (el.id === id) return newData
        return el
      })
    },
  },
})

if (import.meta.hot) {
  import.meta.hot.accept(acceptHMRUpdate(useStaffStore, import.meta.hot))
}
