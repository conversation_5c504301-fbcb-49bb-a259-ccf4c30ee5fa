import { defineStore, acceptHMRUpdate } from 'pinia'
import type { UserRoleType } from 'src/components/models'
import { ACUS, ADMIN_UIDS, MMM_CRED } from 'src/components/variable'
import { useStaffStore } from 'stores/staff.js'
import { auth } from 'boot/firebase'
//import axios from 'axios';
const getCredentials = () => {
  const credentials = localStorage.getItem(MMM_CRED)
  if (credentials) {
    return JSON.parse(credentials)
  }
  return null
}

export const useAuthStore = defineStore('auth', {
  state: () => ({
    name: (getCredentials()?.name || '') as string,
    role: (getCredentials()?.role || '') as UserRoleType,
    id: (getCredentials()?.id || '') as string,
    query: '',
  }),

  getters: {
    _name: (state) => state.name,
    _role: () => {
      const uid = auth.currentUser?.uid
      if (uid) {
        const isAdmin = ADMIN_UIDS.includes(uid)
        if (isAdmin) return 'Admin'
      }
      return 'User'
    },
    _id: (state) => state.id,
  },

  actions: {
    async getUserData() {},
    async login(uid: string) {
      const staffStore = useStaffStore()
      const isAdmin = ADMIN_UIDS.includes(uid)
      if (isAdmin) {
        this.role = 'Admin'
        this.name = 'Admin'
        this.id = uid
        localStorage.setItem(
          MMM_CRED,
          JSON.stringify({
            name: this.name,
            role: this.role,
            id: this.id,
          }),
        )
        return { name: this.name, role: this.role, id: this.id }
      }
      const user = await staffStore.login(uid)
      if (user) {
        this.name = user.name
        this.role = 'Staff'
        this.id = user.id
        localStorage.setItem(
          MMM_CRED,
          JSON.stringify({
            ...user,
            role: 'Staff',
          }),
        )
      }
      return user
    },
    async logout() {
      this.name = ''
      this.role = '' as UserRoleType
      localStorage.removeItem(MMM_CRED)
    },
    clear() {
      this.name = ''
      this.role = '' as UserRoleType
    },
    init() {
      try {
        const user = getCredentials()
        this.role = user?.role || ''
        this.name = user?.name || ''
        this.id = user?.id || ''
      } catch {
        console.error('INIT WARNING')
      }
    },
    getQuery() {
      const uid = auth.currentUser?.uid
      if (uid) {
        const isAdmin = ADMIN_UIDS.includes(uid)
        const isAcus = ACUS.includes(uid)
        if (isAdmin) return ''
        if (isAcus) return `OR(Provider = 'ACU 1', Provider = 'ACU 2')`
        return `Provider = '${this.name}'`
      }
      throw new Error('No user logged in')
    },
  },
})

if (import.meta.hot) {
  import.meta.hot.accept(acceptHMRUpdate(useAuthStore, import.meta.hot))
}
