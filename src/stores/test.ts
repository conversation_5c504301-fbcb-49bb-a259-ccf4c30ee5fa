import { defineStore, acceptHMRUpdate } from 'pinia'
import { useStaffStore } from './staff'
import { useClientStore } from './client'
import { useScheduleStore } from './schedule'
import type { AppointmentType, EventType, ProviderField, StatusType } from 'src/components/models'
//import { NEW_APPOINTMENTS } from 'src/components/test'
import { NEW_APPOINTMENTS } from 'src/components/5-24'
import moment from 'moment'
type APPOINTMENT_TYPE = {
  Status: StatusType
  'Event Type': EventType
  Date: string
  'Appointment Type': AppointmentType
  'Staff Name': string
  Duration: string
  'Customer Name': string
}
export const useTestStore = defineStore('test', {
  state: () => ({
    counter: 0,
  }),

  getters: {
    doubleCount: (state) => state.counter * 2,
  },

  actions: {
    async uploadBulkAppointment() {
      const scheduleStore = useScheduleStore()
      const staffStore = useStaffStore()
      const clientStore = useClientStore()
      const { _staffs } = staffStore
      const { _clients } = clientStore
      let rawAppointments = NEW_APPOINTMENTS as APPOINTMENT_TYPE[]

      rawAppointments = rawAppointments.filter((el) => !!el['Staff Name'])
      const fields: ProviderField[] = []
      console.log(rawAppointments.length)
      for (let i = 0; i < rawAppointments.length; i++) {
        const _appointment = rawAppointments[i]
        if (_appointment) {
          const provider = _staffs.find((staff) => staff.name === _appointment['Staff Name'])
          const patient = _clients.find(
            (client) => client.fields['Patient Name'] === _appointment['Customer Name'],
          )
          const m_Start = moment(_appointment['Date'], 'MM/DD/YYYY HH:mm')
          const appointment: AppointmentType = _appointment['Appointment Type']
          const duration = _appointment.Duration
          const field: ProviderField = {
            id: '',
            name: _appointment['Customer Name'],
            event: 'Appointment',
            start: m_Start.subtract(3, 'hours').toDate(),
            end: m_Start.toDate(),
            duration: duration,
            notes: '',
            appointment: appointment,
            patientId: patient ? [patient.id] : [],
            patientName: patient ? [patient.fields['Patient Name']] : [],
            status: _appointment.Status,
            split: 1,
            pid: provider ? provider.id : '',
          }
          if (provider) fields.push(field)
        }
      }
      console.log(fields)
      const chunks = this.splitArray(fields)
      for (let i = 0; i < chunks.length; i++) {
        console.log(i)
        const fields = chunks[i]
        if (fields) {
          await Promise.all(
            fields?.map((field) => scheduleStore.createNewAppointement(field.pid || '', field)),
          )
        }
        console.log('==============>')
        await this.sleep()
      }

      /*
      console.log(_staffs)
      const missing_staff = NEW_APPOINTMENTS.filter((appointment) => {
        const staff = _staffs.find((staff) => appointment['Staff Name'] === staff.name)
        return !staff
      })
      console.log('staff', [...new Set(missing_staff.map((el) => el['Staff Name']))])
      const missing_client = NEW_APPOINTMENTS.filter((appointment) => {
        const client = _clients.find(
          (client) => client.fields['Patient Name'] === appointment['Customer Name'],
        )
        return !client
      })
      const missing_clients = [...new Set(missing_client.map((el) => el['Customer Name']))]
      console.log('client:', missing_clients)

      const empties = NEW_APPOINTMENTS.filter((el) => !el['Staff Name'].trim())
      console.log('empty', empties)
      */
    },
    splitArray(arr: ProviderField[], size = 30) {
      const result: ProviderField[][] = []
      for (let i = 0; i < arr.length; i += size) {
        result.push(arr.slice(i, i + size))
      }
      return result
    },
    sleep() {
      return new Promise((resolve) =>
        setTimeout(() => {
          resolve(true)
        }, 500),
      )
    },
  },
})

if (import.meta.hot) {
  import.meta.hot.accept(acceptHMRUpdate(useTestStore, import.meta.hot))
}
