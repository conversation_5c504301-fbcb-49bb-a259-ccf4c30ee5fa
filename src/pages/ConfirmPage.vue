<template>
  <div class="fullscreen bg-blue text-white text-center q-pa-md flex flex-center">
    <div>
      <div class="text-h4 q-mb-md">Appointment Confirmation</div>
      
      <div v-if="loading" class="q-my-md">
        <q-spinner size="3em" color="white" />
        <div class="q-mt-sm">Processing your confirmation...</div>
      </div>
      
      <div v-if="error" class="text-negative q-mt-md">
        <div class="text-body1">Error:</div>
        <pre class="text-left bg-dark q-pa-sm rounded-borders">{{ error }}</pre>
        
        <div class="q-mt-md">
          <q-btn color="white" text-color="blue" label="Try Again" @click="init" />
        </div>
      </div>
      
      <div v-if="!loading && !error" class="q-mt-lg">
        <q-icon name="check_circle" size="4em" color="white" />
        <div class="text-h5 q-mt-md">Thank you!</div>
        <div class="text-body1 q-mt-sm">Your appointment has been confirmed.</div>
        
        <div class="q-mt-xl">
          <q-btn 
            color="white" 
            text-color="blue" 
            label="Close" 
            @click="closeWindow" 
            class="q-px-md"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router'
import { onMounted, ref } from 'vue'
import moment from 'moment'
import { useScheduleStore } from 'stores/schedule.js'

const route = useRoute()
const router = useRouter()
const store = useScheduleStore()
const loading = ref(true)
const error = ref('')

onMounted(() => {
  init()
})

const closeWindow = () => {
  // For mobile devices, this might not work due to security restrictions
  // but it's worth trying
  window.close()
}

const init = async () => {
  loading.value = true
  error.value = ''
  
  try {
    // First, check for the records parameter
    const recordsParam = route.query.records
    
    if (recordsParam && typeof recordsParam === 'string') {
      try {
        // Parse the records parameter
        const recordIds = JSON.parse(decodeURIComponent(recordsParam))
        
        if (Array.isArray(recordIds) && recordIds.length > 0) {
          // Process each record ID
          try {
            await Promise.all(recordIds.map(recordId => store.setConfirm(recordId)))
            loading.value = false
            return
          } catch (confirmError) {
            error.value = `Unable to confirm appointment. Please try again later.`
            loading.value = false
            return
          }
        } else {
          error.value = 'No valid appointment information found'
          loading.value = false
          return
        }
      } catch (parseError) {
        error.value = `Invalid confirmation link`
        loading.value = false
        return
      }
    }
    
    // Fall back to the original date-based logic
    const queryDate = route.query.d || ''
    
    if (!queryDate) {
      error.value = 'No appointment information found'
      loading.value = false
      return
    }
    
    if (typeof queryDate !== 'string') {
      error.value = 'Invalid appointment information'
      loading.value = false
      return
    }
    
    try {
      const start = moment(queryDate, 'MM-DD-YYYY').add(4, 'hours').toDate()
      const end = moment(queryDate, 'MM-DD-YYYY').add(1, 'day').add(4, 'hours').toDate()
      const records = await store.getSchedulesFromDateRange(start, end)
      const recordIds = records.map((el) => el.id)
      await Promise.all(recordIds.map((recordId) => store.setConfirm(recordId)))
      loading.value = false
    } catch (dateError) {
      error.value = `Unable to confirm appointment. Please try again later.`
      loading.value = false
    }
  } catch (generalError) {
    error.value = `An unexpected error occurred. Please try again later.`
    loading.value = false
  }
}
</script>

<style scoped>
pre {
  max-width: 100%;
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
