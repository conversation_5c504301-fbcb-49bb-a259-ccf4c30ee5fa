<template>
  <q-page class="flex flex-center">
    <q-card class="auth-card">
      <q-card-section>
        <div class="text-h6">Sign In</div>
      </q-card-section>
      <q-card-section>
        <q-form @submit="onSubmit" class="q-gutter-md">
          <q-input
            v-model="email"
            label="Email"
            type="email"
            :rules="[(val) => !!val || 'Email is required']"
          />

          <q-input
            v-model="password"
            label="Password"
            :type="isPwd ? 'password' : 'text'"
            :rules="[
              (val) => !!val || 'Password is required',
              (val) => val.length >= 8 || 'Password must be at least 8 characters',
              (val) => /[A-Z]/.test(val) || 'Password must contain at least one uppercase letter',

              (val) => /[a-z]/.test(val) || 'Password must contain at least one lowercase letter',
              (val) => /[0-9]/.test(val) || 'Password must contain at least one number',
              /*
              (val) =>
                /[!@#$%^&*]/.test(val) ||
                'Password must contain at least one special character (!@#$%^&*)',
                */
            ]"
          >
            <template v-slot:append>
              <q-icon
                :name="isPwd ? 'visibility_off' : 'visibility'"
                class="cursor-pointer"
                @click="isPwd = !isPwd"
              />
            </template>
          </q-input>

          <div>
            <q-btn
              label="Sign In"
              type="submit"
              color="primary"
              :loading="loading"
              :disable="loading"
            />
          </div>
        </q-form>
      </q-card-section>
    </q-card>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuth } from 'src/composables/useAuth'
import { useQuasar } from 'quasar'
import { useCenterStore } from 'stores/center'
const $q = useQuasar()
const router = useRouter()
const { signIn } = useAuth()
const centerStore = useCenterStore()
const email = ref('')
const password = ref('')
const isPwd = ref(true)
const loading = ref(false)
onMounted(() => {
  centerStore.init()
})
const onSubmit = async () => {
  try {
    loading.value = true
    await signIn(email.value, password.value)
    router.push('/')
  } catch {
    $q.notify({
      color: 'negative',
      message: 'Failed to sign in',
    })
  }
  loading.value = false
}
</script>

<style scoped>
.auth-card {
  width: 100%;
  max-width: 400px;
}
</style>
