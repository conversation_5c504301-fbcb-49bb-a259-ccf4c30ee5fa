import type { RouteRecordRaw } from 'vue-router'
import { requireAuth, requireNoAuth } from './guards'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    component: () => import('layouts/MainLayout.vue'),
    beforeEnter: requireAuth,
    children: [
      { 
        path: '', 
        component: () => import('pages/IndexPage.vue'),
      }
    ],
  },
  {
    path: '/login',
    component: () => import('layouts/PublicLayout.vue'),
    beforeEnter: requireNoAuth,
    children: [{ path: '', component: () => import('pages/SignIn.vue') }],
  },
  {
    path: '/c',
    component: () => import('layouts/PublicLayout.vue'),
    children: [{ path: '', component: () => import('pages/ConfirmPage.vue') }],
  },
  // Always leave this as last one,
  // but you can also remove it
  {
    path: '/:catchAll(.*)*',
    component: () => import('pages/ErrorNotFound.vue'),
  },
]

export default routes
