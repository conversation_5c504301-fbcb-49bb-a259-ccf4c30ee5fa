import type { NavigationGuardWithThis } from 'vue-router'
import { auth } from 'src/boot/firebase'
import { MMM_CRED } from 'src/components/variable'

const getCredentials = () => {
  const credentials = localStorage.getItem(MMM_CRED)
  if (credentials) {
    return JSON.parse(credentials)
  }
  return null
}

export const requireAuth: NavigationGuardWithThis<undefined> = (to, from, next) => {
  const unsubscribe = auth.onAuthStateChanged((user) => {
    const crentials = getCredentials()
    if (user && crentials) {
      next()
    } else {
      next('/login')
    }
    unsubscribe()
  })
}

export const requireNoAuth: NavigationGuardWithThis<undefined> = (to, from, next) => {
  const unsubscribe = auth.onAuthStateChanged((user) => {
    const crentials = getCredentials()
    if (!user || !crentials) {
      next()
    } else {
      next('/')
    }
    unsubscribe()
  })
}
