<template>
  <q-layout style="height: 100vh" view="lHh Lpr lff" container>
    <q-header class="bg-grey-2 mma-header items-center">
      <q-toolbar class="full-height">
        <q-btn class="lt-md" size="sm" color="grey" icon="menu" round rounded @click="onOpen" />
        <div style="display: none">
          <q-btn
            outline
            class="q-px-xl gt-sm"
            color="grey-9"
            label="Upload"
            rounded
            @click="onUpload"
            no-caps
          />
        </div>
        <q-btn
          outline
          class="q-px-xl gt-sm"
          color="grey-9"
          label="Today"
          rounded
          @click="onToday"
          no-caps
        />
        <q-btn
          icon="arrow_back_ios"
          dense
          class="q-ml-xl gt-sm"
          color="grey-9"
          flat
          round
          :ripple="false"
          @click="onPrev"
        />
        <q-btn
          class="q-ml-sm gt-sm"
          icon="arrow_forward_ios"
          color="grey-9"
          flat
          round
          :ripple="false"
          dense
          @click="onNext"
        />
        <div class="text-grey-9 q-ml-md text-h6 gt-sm">{{ selectedDate }}</div>
        <q-space />
        <q-input
          dense
          filled
          v-model="patientName"
          placeholder="Search by patient"
          unelevated
          no-caps
          class="gt-sm"
        >
          <template v-slot:prepend>
            <q-icon name="search" />
          </template>
        </q-input>
        <q-btn color="grey" size="sm" round label="A" class="q-ml-md">
          <q-menu>
            <div style="width: 150px">
              <q-item clickable v-close-popup @click="onExit">
                <q-item-section avatar>
                  <q-icon color="primary" name="logout" />
                </q-item-section>
                <q-item-section>Log out</q-item-section>
              </q-item>
            </div>
          </q-menu>
        </q-btn>
      </q-toolbar>
    </q-header>
    <left-drawer />
    <q-page-container>
      <router-view />
    </q-page-container>
  </q-layout>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import LeftDrawer from 'components/LeftDrawer.vue'
import { useFilterStore } from 'stores/filter'
import { useScheduleStore } from 'stores/schedule'
import { useTestStore } from 'stores/test'
import { useAuth } from 'src/composables/useAuth'
import { MMM_CRED, US_TIME_FORMAT } from 'src/components/variable'
import moment from 'moment'
import { computed } from 'vue'
import { storeToRefs } from 'pinia'
const router = useRouter()
const scheduleStore = useScheduleStore()
const filterStore = useFilterStore()
const testStore = useTestStore()
const auth = useAuth()
const { _date, _patientName } = storeToRefs(filterStore)
const selectedDate = computed(() =>
  moment(_date.value, US_TIME_FORMAT).format('dddd, MMMM D, YYYY'),
)
const patientName = computed({
  get: () => _patientName.value,
  set: (val) => filterStore.changePatient(val),
})
const onExit = async () => {
  localStorage.removeItem(MMM_CRED)
  await auth.logout()
  router.push('/login')
}
const onToday = () => {
  const date = moment().format(US_TIME_FORMAT)
  filterStore.changeDate(date)
}
const onPrev = async () => {
  const prevMoment = moment(_date.value, US_TIME_FORMAT).subtract(1, 'day')
  const hasSchedule = scheduleStore.hasSchedule(prevMoment)
  filterStore.changeDate(prevMoment.format(US_TIME_FORMAT))
  if (!hasSchedule) {
    filterStore.loading = true
    await scheduleStore.getSchedules()
  }
  filterStore.loading = false
}
const onNext = async () => {
  const nextMoment = moment(_date.value, US_TIME_FORMAT).add(1, 'day')
  const hasSchedule = scheduleStore.hasSchedule(nextMoment)
  filterStore.changeDate(nextMoment.format(US_TIME_FORMAT))
  if (!hasSchedule) {
    filterStore.loading = true
    await scheduleStore.getSchedules()
  }
  filterStore.loading = false
}
const onOpen = () => {
  filterStore.setOpen()
}
const onUpload = () => {
  testStore.uploadBulkAppointment()
}
</script>
<style lang="scss">
.mma-header {
  height: 72px;
  border: none;
  .q-input {
    border-radius: 10px;
    min-width: 160px;
    background-color: #e9eef6;
    .q-field__control {
      border-radius: 10px;
      &::after {
        background: transparent;
      }
    }
  }
}
</style>
