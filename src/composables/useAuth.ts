import { ref, onMounted, onUnmounted } from 'vue'
import { signInWithEmailAndPassword, signOut, onAuthStateChanged, type User } from 'firebase/auth'
import { auth } from 'src/boot/firebase'
import { useRouter } from 'vue-router'
import { useAuthStore } from 'stores/user'
const user = ref<User | null>(null)
const loading = ref(true)
const INACTIVITY_TIMEOUT = 15 * 60 * 1000 // 15 minutes in milliseconds
let inactivityTimer: NodeJS.Timeout | null = null

export function useAuth() {
  const router = useRouter()

  const resetInactivityTimer = () => {
    if (inactivityTimer) {
      clearTimeout(inactivityTimer)
    }
    if (user.value) {
      inactivityTimer = setTimeout(async () => {
        await logout()
        router.push('/login')
      }, INACTIVITY_TIMEOUT)
    }
  }

  const handleUserActivity = () => {
    resetInactivityTimer()
  }

  onMounted(() => {
    // Add event listeners for user activity
    window.addEventListener('mousemove', handleUserActivity)
    window.addEventListener('keydown', handleUserActivity)
    window.addEventListener('mousedown', handleUserActivity)
    window.addEventListener('touchstart', handleUserActivity)
    window.addEventListener('scroll', handleUserActivity)
  })

  onUnmounted(() => {
    // Clean up event listeners
    window.removeEventListener('mousemove', handleUserActivity)
    window.removeEventListener('keydown', handleUserActivity)
    window.removeEventListener('mousedown', handleUserActivity)
    window.removeEventListener('touchstart', handleUserActivity)
    window.removeEventListener('scroll', handleUserActivity)

    if (inactivityTimer) {
      clearTimeout(inactivityTimer)
    }
  })

  onAuthStateChanged(auth, (_user) => {
    user.value = _user
    loading.value = false
    if (_user) {
      resetInactivityTimer()
    } else if (inactivityTimer) {
      clearTimeout(inactivityTimer)
    }
  })

  const signIn = async (email: string, password: string) => {
    const userCredential = await signInWithEmailAndPassword(auth, email, password)
    resetInactivityTimer()
    const authStore = useAuthStore()
    await authStore.login(userCredential.user.uid)
    return userCredential.user
  }

  const logout = async () => {
    if (inactivityTimer) {
      clearTimeout(inactivityTimer)
    }
    await signOut(auth)
  }

  return {
    user,
    loading,
    signIn,
    logout,
  }
}
