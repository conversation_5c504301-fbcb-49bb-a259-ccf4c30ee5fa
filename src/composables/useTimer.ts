import { onMounted, onUnmounted } from 'vue'
import { useScheduleStore } from 'stores/schedule'
import { useStaffStore } from 'stores/staff'
import { useClientStore } from 'stores/client.js'

export const useTimer = (interval = 5000) => {
  let timer: NodeJS.Timeout | string | number | undefined | null = null
  const scheduleStore = useScheduleStore()
  const staffStore = useStaffStore()
  const clientStore = useClientStore()
  const start = () => {
    if (timer) return // Prevent starting a new timer if one is already running
    timer = setInterval(() => {
      Promise.all([scheduleStore.getUpdates(), clientStore.getUpdates(), staffStore.getUpdates()])
        .then(() => {})
        .catch(() => {})
    }, interval)
  }

  const stop = () => {
    if (timer) {
      clearInterval(timer)
      timer = null
    }
  }
  // Automatically start and clean up the timer when the component is mounted and unmounted
  onMounted(start)
  onUnmounted(stop)
}
