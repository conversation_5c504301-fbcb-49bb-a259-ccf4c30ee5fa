import moment from 'moment'
import { computed } from 'vue'
import { Platform } from 'quasar'
import { storeToRefs } from 'pinia'
import type { AppointmentType, EventType, ProviderField } from 'src/components/models'
import { GROUP_COLORS, US_TIME_FORMAT } from 'src/components/variable'
import { useFilterStore } from 'src/stores/filter'
import { useCenterStore } from 'stores/center'
import { useClientStore } from 'stores/client'
import { useAuthStore } from 'stores/user'
import { compareDate } from 'src/utils'

export const useEvent = () => {
  const centerStore = useCenterStore()
  const filterStore = useFilterStore()
  const clientStore = useClientStore()
  const authStore = useAuthStore()
  const { role, name } = storeToRefs(authStore)
  const { providers } = storeToRefs(centerStore)
  const { _date, providerId, eventType, appointmentType, patientName } = storeToRefs(filterStore)
  const { _clients } = storeToRefs(clientStore)

  const selectedDate = computed(() => {
    return moment(_date.value, US_TIME_FORMAT).toDate()
  })
  const _providers = computed(() => {
    const length = providers.value.length
    let sortProviders = [...providers.value]
    sortProviders = sortProviders.sort((a, b) => {
      if (role.value === 'Staff') {
        if (name.value.includes('ACU ')) {
          if (a.name.includes('ACU')) return -1
          if (b.name.includes('ACU')) return 1
          return a.name.localeCompare(b.name)
        } else {
          if (a.name === name.value) return -1
          if (b.name === name.value) return 1
          return -1
        }
      }

      if (!a.role || !b.role) return 1
      const roleValue = b.role.localeCompare(a.role)
      if (roleValue !== 0) return roleValue
      return a.name.localeCompare(b.name)
    })
    return sortProviders
      .filter((el) => {
        if (providerId.value && providerId.value !== el.id) return false
        if (el.startDate && compareDate(el.startDate, selectedDate.value, 'before')) {
          return false
        }
        if (el.inactiveDate && !compareDate(el.inactiveDate, selectedDate.value, 'before')) {
          return false
        }
        const flag = el.workingDays.some((el) => el.day === moment(selectedDate.value).weekday())
        if (!flag) {
          return el.fields.find((field) => compareDate(field.start, selectedDate.value, 'same'))
        }
        return true
      })
      .map((el, index) => ({
        ...el,
        id: index + 1,
        label: el.name,
        class:
          el.role === 'PA' || el.role === 'NP'
            ? `split${(index % length) + 1} np-pa-flex-width`
            : `split${(index % length) + 1}`,
        pid: el.id,
      }))
  })
  const allEvents = computed(() => {
    let events: ProviderField[] = []
    const cancelledEvents: ProviderField[] = []
    _providers.value.forEach((provider, index) => {
      provider.fields.forEach((field) => {
        const flag = filter(
          field,
          selectedDate.value,
          eventType.value,
          appointmentType.value,
          patientName.value,
        )
        if (flag) {
          if (field.event === 'Appointment') {
            const client = _clients.value.find((client) => field.patientId.includes(client.id))
            if (client) {
              const event = {
                ...field,
                split: index + 1,
                pid: provider.pid,
                isNewPatient: !client.fields['Last Visit Date'] || false,
              }
              if (field.status !== 'Cancelled') events.push(event)
              else cancelledEvents.push(event)
            }
          } else if (field.event === 'Time Off') {
            events.push({
              ...field,
              split: index + 1,
              pid: provider.pid,
              isNewPatient: false,
            })
          }
        }
      })
    })
    const clientIds = events.map((el) => el.patientId).flat()
    const counts = clientIds.reduce(
      (acc, element) => {
        if (acc[element]) {
          acc[element]++
        } else {
          acc[element] = 1
        }
        return acc
      },
      {} as Record<string, number>,
    )
    const groups = Object.entries(counts)
      .map(([id, count]) => ({
        id,
        count,
      }))
      .filter((el) => el.count > 1)
    events = events.map((el) => {
      const patiendId = el.patientId[0]
      const index = groups.findIndex((el) => el.id === patiendId)
      const cancelled = cancelledEvents.filter((cancelledEvent) => {
        return (
          cancelledEvent.pid === el.pid &&
          cancelledEvent.start === el.start &&
          cancelledEvent.duration === el.duration
        )
      })
      const event = { ...el, cancelled }
      if (index < 0) return event
      return { ...event, group: GROUP_COLORS[index % GROUP_COLORS.length] } as ProviderField
    })
    return events
  })
  const timeRange = computed(() => {
    const workingHours = _providers.value
      .filter((provider) =>
        provider.workingDays.some((el) => el.day === moment(selectedDate.value).weekday()),
      )
      .map((el) => el.workingDays)
      .flat()
      .filter((el) => el.day === moment(selectedDate.value).weekday())
    if (workingHours.length === 0) return { min: 12 * 60, max: 14 * 60, rMin: 12, rMax: 14 }
    const minH = Math.min(...workingHours.map((el) => el.from)) - 1
    const maxH = Math.max(...workingHours.map((el) => el.to)) + 1
    return {
      min: minH * 60,
      max: maxH * 60,
      rMin: minH,
      rMax: maxH,
      height: !Platform.is.mobile
        ? Math.floor((window.screen.availHeight - 264) / ((maxH - minH) * 2))
        : 50,
    }
  })
  const selectedDateEvents = computed(() => {
    const _events: ProviderField[] = [...allEvents.value]
    _providers.value.forEach((provider, index) => {
      const workingDay = provider.workingDays.find(
        (el) => el.day === moment(selectedDate.value).weekday(),
      )
      const field = {
        name: '',
        status: 'No Show',
        duration: '',
        notes: 'event',
        event: 'Appointment',
        appointment: 'Acupuncture',
        id: '',
        patientId: [] as string[],
        patientName: [] as string[],
        split: index + 1,
        non_event: true,
      } as ProviderField
      if (workingDay) {
        const { rMin, rMax } = timeRange.value
        if (rMin < workingDay.from) {
          _events.push({
            ...field,
            start: moment(selectedDate.value).hours(rMin).minutes(0).format('YYYY-MM-DD HH:mm'),
            end: moment(selectedDate.value)
              .hours(workingDay.from)
              .minutes(0)
              .format('YYYY-MM-DD HH:mm'),
            non_event: true,
            background: true,
          })
        }
        if (rMax > workingDay.to) {
          _events.push({
            ...field,
            start: moment(selectedDate.value)
              .hours(workingDay.to)
              .minutes(0)
              .format('YYYY-MM-DD HH:mm'),
            end: moment(selectedDate.value).hours(rMax).minutes(0).format('YYYY-MM-DD HH:mm'),
            non_event: true,
            background: true,
          })
        }
      }
    })
    return _events
  })
  const filter = (
    field: ProviderField,
    selectedDate: Date,
    eventType: EventType,
    appointmentType: AppointmentType,
    patientName: string,
  ) => {
    const momentA = moment(selectedDate)
    const momentB = moment(field.start, 'YYYY-MM-DD')
    if (!momentA.isSame(momentB)) return false
    if (eventType) return field.event === eventType
    if (appointmentType) return field.appointment === appointmentType
    if (patientName) {
      const name = field.patientName[0]
      if (!name) return false
      return name.toLowerCase().includes(patientName.toLowerCase())
    }
    return true
  }
  return {
    providers: _providers,
    timeRange,
    events: selectedDateEvents,
    selectedDate,
  }
}
