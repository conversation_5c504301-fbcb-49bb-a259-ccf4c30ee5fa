[debug] [2025-05-28T13:05:25.552Z] ----------------------------------------------------------------------
[debug] [2025-05-28T13:05:25.554Z] Command:       /opt/homebrew/Cellar/node@20/20.18.3/bin/node /opt/homebrew/bin/firebase deploy --only hosting
[debug] [2025-05-28T13:05:25.554Z] CLI Version:   14.2.2
[debug] [2025-05-28T13:05:25.554Z] Platform:      darwin
[debug] [2025-05-28T13:05:25.554Z] Node Version:  v20.18.3
[debug] [2025-05-28T13:05:25.554Z] Time:          Wed May 28 2025 15:05:25 GMT+0200 (Central European Summer Time)
[debug] [2025-05-28T13:05:25.554Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-05-28T13:05:25.637Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-05-28T13:05:25.637Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-05-28T13:05:25.637Z] [iam] checking project mmawellness-2230c for permissions ["firebase.projects.get","firebasehosting.sites.update"]
[debug] [2025-05-28T13:05:25.638Z] Checked if tokens are valid: true, expires at: 1748441116929
[debug] [2025-05-28T13:05:25.638Z] Checked if tokens are valid: true, expires at: 1748441116929
[debug] [2025-05-28T13:05:25.638Z] >>> [apiv2][query] POST https://cloudresourcemanager.googleapis.com/v1/projects/mmawellness-2230c:testIamPermissions [none]
[debug] [2025-05-28T13:05:25.638Z] >>> [apiv2][(partial)header] POST https://cloudresourcemanager.googleapis.com/v1/projects/mmawellness-2230c:testIamPermissions x-goog-quota-user=projects/mmawellness-2230c
[debug] [2025-05-28T13:05:25.638Z] >>> [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/mmawellness-2230c:testIamPermissions {"permissions":["firebase.projects.get","firebasehosting.sites.update"]}
